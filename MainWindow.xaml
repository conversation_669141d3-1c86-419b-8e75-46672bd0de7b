﻿<Window x:Class="API.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:API"
        mc:Ignorable="d"
        Title="MainWindow" Height="600" Width="1000">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>
        <!-- 控制面板 -->
        <StackPanel Orientation="Horizontal">
            <Button Content="登录" Click="Login_Click"/>
            <ComboBox x:Name="LayerSelector" Width="100" Margin="10">
                <ComboBoxItem>图层1</ComboBoxItem>
                <ComboBoxItem>图层2</ComboBoxItem>
            </ComboBox>
            <Button Content="关闭窗口" Click="CloseLayer_Click"/>
            <Button Content="置顶" Click="BringToFront_Click"/>
        </StackPanel>
        <!-- 模拟显示屏 -->
        <Canvas x:Name="DisplayCanvas" Grid.Row="1" Background="Black" MouseMove="Canvas_MouseMove" MouseLeftButtonUp="Canvas_MouseUp">
            <Border x:Name="Layer1" Background="Blue" Width="100" Height="100" Canvas.Left="100" Canvas.Top="100" MouseLeftButtonDown="Layer_MouseDown"/>
            <Border x:Name="Layer2" Background="Red" Width="100" Height="100" Canvas.Left="300" Canvas.Top="300" MouseLeftButtonDown="Layer_MouseDown"/>
        </Canvas>
    </Grid>
</Window>
