﻿<Window x:Class="API.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:API"
        mc:Ignorable="d"
        Title="设备控制模拟器 - LED拼接屏控制系统" Height="800" Width="1400">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 主控制面板 -->
        <GroupBox Header="设备控制" Grid.Row="0" Margin="5">
            <StackPanel Orientation="Horizontal" Margin="5">
                <!-- 模拟模式控制 -->
                <CheckBox x:Name="SimulationModeCheckBox" Content="模拟模式" IsChecked="True"
                         VerticalAlignment="Center" Margin="5,0" FontWeight="Bold"
                         Foreground="Blue" Checked="SimulationMode_Changed" Unchecked="SimulationMode_Changed"
                         ToolTip="开启后使用模拟数据测试，关闭后连接真实设备"/>
                <CheckBox x:Name="SimulateErrorsCheckBox" Content="模拟错误" IsChecked="False"
                         VerticalAlignment="Center" Margin="5,0" FontWeight="Bold"
                         Foreground="Orange" Checked="SimulateErrors_Changed" Unchecked="SimulateErrors_Changed"
                         ToolTip="开启后偶尔返回错误响应，用于测试错误处理"/>
                <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="10,0"/>

                <Button x:Name="LoginButton" Content="登录设备" Click="Login_Click" Width="80" Margin="5"/>
                <TextBlock x:Name="ConnectionStatus" Text="未连接" VerticalAlignment="Center" Margin="10,0"
                          Foreground="Red" FontWeight="Bold"/>
                <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="10,0"/>
                <TextBlock Text="图层选择:" VerticalAlignment="Center" Margin="5,0"/>
                <ComboBox x:Name="LayerSelector" Width="100" Margin="5" SelectedIndex="0">
                    <ComboBoxItem>图层1</ComboBoxItem>
                    <ComboBoxItem>图层2</ComboBoxItem>
                    <ComboBoxItem>图层3</ComboBoxItem>
                    <ComboBoxItem>图层4</ComboBoxItem>
                </ComboBox>
                <Button Content="新建窗口" Click="CreateLayer_Click" Width="80" Margin="5"/>
                <Button Content="关闭窗口" Click="CloseLayer_Click" Width="80" Margin="5"/>
                <Button Content="置顶" Click="BringToFront_Click" Width="60" Margin="5"/>
            </StackPanel>
        </GroupBox>

        <!-- 场景和信号源控制 -->
        <GroupBox Header="场景管理与信号源" Grid.Row="1" Margin="5">
            <StackPanel Orientation="Horizontal" Margin="5">
                <TextBlock Text="预设场景:" VerticalAlignment="Center" Margin="5,0"/>
                <Button Content="实验投屏" Click="ExperimentScene_Click" Width="80" Margin="5" Background="LightBlue"/>
                <Button Content="演讲场景" Click="PresentationScene_Click" Width="80" Margin="5" Background="LightGreen"/>
                <Button Content="监控场景" Click="MonitorScene_Click" Width="80" Margin="5" Background="LightYellow"/>
                <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" Margin="10,0"/>
                <TextBlock Text="信号源:" VerticalAlignment="Center" Margin="5,0"/>
                <ComboBox x:Name="InputSourceSelector" Width="120" Margin="5"/>
                <Button Content="刷新信号源" Click="RefreshInputSources_Click" Width="90" Margin="5"/>
                <Button Content="应用到图层" Click="ApplyInputToLayer_Click" Width="90" Margin="5"/>
            </StackPanel>
        </GroupBox>

        <!-- 模拟显示屏区域 -->
        <GroupBox Header="LED拼接屏模拟显示 (7680x4320)" Grid.Row="2" Margin="5">
            <Grid>
                <Canvas x:Name="DisplayCanvas" Background="Black" MouseMove="Canvas_MouseMove"
                       MouseLeftButtonUp="Canvas_MouseUp" ClipToBounds="True">
                    <!-- 网格线 -->
                    <Canvas x:Name="GridCanvas" IsHitTestVisible="False"/>
                    <!-- 动态生成的图层将在这里显示 -->
                </Canvas>
                <!-- 坐标显示 -->
                <TextBlock x:Name="CoordinateDisplay" Text="坐标: (0, 0)"
                          HorizontalAlignment="Right" VerticalAlignment="Top"
                          Background="Black" Foreground="White" Padding="5" Margin="10"/>
            </Grid>
        </GroupBox>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="3">
            <StatusBarItem>
                <TextBlock x:Name="StatusText" Text="就绪"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock x:Name="LayerCountText" Text="图层数: 0"/>
            </StatusBarItem>
            <Separator/>
            <StatusBarItem>
                <TextBlock x:Name="ResolutionText" Text="分辨率: 7680x4320"/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
