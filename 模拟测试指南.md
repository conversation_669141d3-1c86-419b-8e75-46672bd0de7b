# LED拼接屏控制系统 - 模拟测试指南

## 概述
为了在没有实际设备的情况下测试系统功能，我们实现了完整的模拟模式。模拟模式使用预定义的模拟数据来响应所有API调用，让您可以完整体验系统的所有功能。

## 模拟模式特性

### 🎯 完整功能模拟
- ✅ 所有API接口都有对应的模拟响应
- ✅ 模拟网络延迟（200-500ms）
- ✅ 真实的JSON响应数据
- ✅ 完整的错误处理流程

### 🔄 模式切换
- **模拟模式**：使用模拟数据，无需真实设备
- **真实设备模式**：连接实际LED拼接屏设备
- **一键切换**：界面左上角复选框控制

## 测试环境启动

### 1. 启动应用
```bash
# 编译并运行项目
dotnet run
# 或者在Visual Studio中按F5
```

### 2. 确认模拟模式
- 启动后默认开启模拟模式
- 界面标题显示：`[模拟模式]`
- 左上角"模拟模式"复选框已勾选
- 状态栏显示："模拟模式已启用"

## 功能测试清单

### 📋 基础连接测试

#### 测试步骤：
1. **登录测试**
   - 点击"登录设备"按钮
   - 观察连接状态变为"已连接"（绿色）
   - 状态栏显示"登录成功"
   - 按钮变为"已登录"并禁用

#### 预期结果：
```json
模拟响应：
{
  "status": 0,
  "message": "登录成功",
  "data": {
    "token": "mock_token_12345",
    "userInfo": {
      "id": 1,
      "username": "admin",
      "role": "administrator"
    }
  }
}
```

### 📋 图层操作测试

#### 1. 创建图层测试
**操作步骤：**
- 点击"新建窗口"按钮
- 观察显示区域出现新的彩色图层块
- 状态栏显示"图层X创建成功"
- 图层计数增加

**测试要点：**
- 每次创建的图层颜色不同
- 图层位置自动偏移避免重叠
- 图层中心显示编号

#### 2. 图层拖拽测试
**操作步骤：**
- 用鼠标左键点击任意图层
- 拖拽到新位置
- 松开鼠标
- 观察状态栏反馈

**测试要点：**
- 拖拽过程中图层跟随鼠标移动
- 松开鼠标后显示"图层X移动成功"
- 坐标显示实时更新

#### 3. 图层删除测试
**操作步骤：**
- 在图层选择框中选择要删除的图层
- 点击"关闭窗口"按钮
- 观察图层从显示区域消失

**测试要点：**
- 图层立即从界面消失
- 图层计数减少
- 状态栏显示"图层X关闭成功"

#### 4. 图层置顶测试
**操作步骤：**
- 创建多个重叠的图层
- 选择被遮挡的图层
- 点击"置顶"按钮
- 观察图层显示在最前端

### 📋 信号源管理测试

#### 1. 刷新信号源测试
**操作步骤：**
- 点击"刷新信号源"按钮
- 观察信号源下拉框填充数据

**模拟数据：**
- HDMI-1 (HDMI) - 正常
- DVI-1 (DVI) - 正常  
- VGA-1 (VGA) - 离线
- 网络流-1 (Network) - 正常

#### 2. 应用信号源测试
**操作步骤：**
- 选择一个图层
- 选择一个信号源
- 点击"应用到图层"按钮
- 观察状态栏反馈

### 📋 预设场景测试

#### 1. 实验投屏场景
**操作步骤：**
- 点击"实验投屏"按钮
- 观察状态栏显示"实验投屏场景应用成功"

#### 2. 演讲场景
**操作步骤：**
- 点击"演讲场景"按钮
- 观察状态栏显示"演讲场景应用成功"

#### 3. 监控场景
**操作步骤：**
- 点击"监控场景"按钮
- 观察状态栏显示"监控场景应用成功"

### 📋 界面交互测试

#### 1. 坐标显示测试
**操作步骤：**
- 在显示区域移动鼠标
- 观察右上角坐标实时更新
- 验证坐标范围（0-7679, 0-4319）

#### 2. 网格线测试
**操作步骤：**
- 观察显示区域的白色网格线
- 验证网格间距均匀
- 调整窗口大小，观察网格自适应

#### 3. 状态栏信息测试
**操作步骤：**
- 执行各种操作
- 观察状态栏信息实时更新
- 验证图层计数准确性

## 模拟数据详情

### 登录响应
```json
{
  "status": 0,
  "message": "登录成功",
  "data": {
    "token": "mock_token_12345",
    "userInfo": {
      "id": 1,
      "username": "admin", 
      "role": "administrator"
    }
  }
}
```

### 输入源列表
```json
{
  "status": 0,
  "message": "获取输入源列表成功",
  "data": [
    {
      "id": 1,
      "name": "HDMI-1",
      "type": "HDMI",
      "isOnline": true,
      "resolution": "1920x1080",
      "status": "正常"
    },
    {
      "id": 2,
      "name": "DVI-1", 
      "type": "DVI",
      "isOnline": true,
      "resolution": "1920x1080",
      "status": "正常"
    },
    {
      "id": 3,
      "name": "VGA-1",
      "type": "VGA", 
      "isOnline": false,
      "resolution": "1024x768",
      "status": "离线"
    },
    {
      "id": 4,
      "name": "网络流-1",
      "type": "Network",
      "isOnline": true,
      "resolution": "1920x1080", 
      "status": "正常"
    }
  ]
}
```

### 预设场景列表
```json
{
  "status": 0,
  "message": "获取预设场景列表成功",
  "data": [
    {
      "id": 1,
      "name": "实验投屏场景",
      "description": "适用于实验室多屏投屏展示",
      "layers": [...]
    },
    {
      "id": 2, 
      "name": "演讲场景",
      "description": "适用于会议演讲展示",
      "layers": [...]
    }
  ]
}
```

## 测试检查点

### ✅ 必须验证的功能点

1. **界面响应性**
   - [ ] 所有按钮点击有响应
   - [ ] 拖拽操作流畅
   - [ ] 状态信息及时更新

2. **数据一致性**
   - [ ] 图层计数准确
   - [ ] 坐标显示正确
   - [ ] 连接状态正确

3. **错误处理**
   - [ ] 未登录时操作有提示
   - [ ] 无效操作有错误提示
   - [ ] 网络延迟模拟正常

4. **模式切换**
   - [ ] 模拟模式开关正常
   - [ ] 模式切换后状态重置
   - [ ] 标题栏显示正确

## 性能测试

### 大量图层测试
**操作步骤：**
1. 连续创建10-20个图层
2. 观察界面响应速度
3. 测试拖拽性能
4. 验证内存使用情况

### 频繁操作测试
**操作步骤：**
1. 快速连续点击各种按钮
2. 快速拖拽多个图层
3. 观察系统稳定性
4. 检查是否有异常

## 切换到真实设备模式

### 准备工作
1. 确保LED拼接屏设备在线
2. 配置正确的设备IP地址
3. 验证网络连接

### 切换步骤
1. 取消勾选"模拟模式"复选框
2. 观察标题变为"[真实设备模式]"
3. 点击"登录设备"连接真实设备
4. 开始真实设备测试

## 故障排除

### 常见问题

**Q: 模拟模式下登录失败？**
A: 检查SimulationMode是否为true，重启应用程序

**Q: 图层拖拽没有反应？**
A: 确保已经登录，检查图层是否正确创建

**Q: 坐标显示不准确？**
A: 调整窗口大小后坐标会重新计算

**Q: 状态栏信息不更新？**
A: 检查UI线程，重启应用程序

### 调试技巧

1. **查看控制台输出**：观察异常信息
2. **断点调试**：在关键方法设置断点
3. **日志记录**：添加详细的操作日志
4. **单步测试**：逐个功能点测试

## 测试报告模板

### 测试环境
- 操作系统：Windows 10/11
- .NET版本：Framework 4.5.2+
- 测试模式：模拟模式
- 测试时间：[填写时间]

### 测试结果
- [ ] 基础连接功能正常
- [ ] 图层操作功能正常  
- [ ] 信号源管理功能正常
- [ ] 预设场景功能正常
- [ ] 界面交互功能正常
- [ ] 性能表现良好

### 发现问题
1. [问题描述]
2. [问题描述]

### 改进建议
1. [建议内容]
2. [建议内容]

通过这个完整的模拟测试环境，您可以在没有实际设备的情况下全面验证系统的各项功能，确保在连接真实设备之前代码的正确性和稳定性。
