using System;
using System.Collections.Generic;
using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace API
{
    public class DeviceApiClient
    {
        private readonly string _baseUrl = "http://192.168.0.10";
        private readonly CookieContainer _cookies = new CookieContainer();

        // 模拟模式开关 - 设置为true时使用模拟数据，false时连接真实设备
        public bool SimulationMode { get; set; } = true;

        // 模拟错误开关 - 设置为true时偶尔返回错误响应，用于测试错误处理
        public bool SimulateErrors { get; set; } = false;

        public async Task<string> PostAsync(string endpoint, object data)
        {
            if (SimulationMode)
            {
                // 模拟模式：返回模拟数据
                return await SimulateApiResponse(endpoint, data);
            }
            else
            {
                // 真实模式：连接实际设备
                return await RealApiCall(endpoint, data);
            }
        }

        private async Task<string> RealApiCall(string endpoint, object data)
        {
            using (var handler = new HttpClientHandler { CookieContainer = _cookies })
            using (var client = new HttpClient(handler))
            {
                var json = JsonConvert.SerializeObject(data);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var response = await client.PostAsync($"{_baseUrl}{endpoint}", content);
                return await response.Content.ReadAsStringAsync();
            }
        }

        private async Task<string> SimulateApiResponse(string endpoint, object data)
        {
            // 模拟网络延迟
            await Task.Delay(200 + new Random().Next(300));

            // 模拟随机错误（10%概率）
            if (SimulateErrors && new Random().Next(100) < 10)
            {
                return SimulateErrorResponse(endpoint);
            }

            switch (endpoint)
            {
                case "/api/user/login":
                    return SimulateLoginResponse();

                case "/api/layer/writeWindow":
                    return SimulateLayerMoveResponse();

                case "/api/layer/delete":
                    return SimulateLayerDeleteResponse();

                case "/api/layer/create":
                    return SimulateLayerCreateResponse();

                case "/api/layer/writeZIndex":
                    return SimulateZIndexResponse();

                case "/api/layer/setInputSource":
                    return SimulateSetInputSourceResponse();

                case "/api/input/readInputOnlineList":
                    return SimulateInputSourceListResponse();

                case "/api/preset/readPresetList":
                    return SimulatePresetListResponse();

                case "/api/preset/apply":
                    return SimulateApplyPresetResponse();

                case "/api/device/status":
                    return SimulateDeviceStatusResponse();

                default:
                    return SimulateGenericSuccessResponse();
            }
        }

        private string SimulateLoginResponse()
        {
            return @"{
                ""status"": 0,
                ""message"": ""登录成功"",
                ""data"": {
                    ""token"": ""mock_token_12345"",
                    ""userInfo"": {
                        ""id"": 1,
                        ""username"": ""admin"",
                        ""role"": ""administrator""
                    }
                }
            }";
        }

        private string SimulateLayerMoveResponse()
        {
            return @"{
                ""status"": 0,
                ""message"": ""图层移动成功"",
                ""data"": null
            }";
        }

        private string SimulateLayerDeleteResponse()
        {
            return @"{
                ""status"": 0,
                ""message"": ""图层删除成功"",
                ""data"": null
            }";
        }

        private string SimulateLayerCreateResponse()
        {
            return @"{
                ""status"": 0,
                ""message"": ""图层创建成功"",
                ""data"": {
                    ""layerId"": " + new Random().Next(1, 100) + @"
                }
            }";
        }

        private string SimulateZIndexResponse()
        {
            return @"{
                ""status"": 0,
                ""message"": ""图层层级设置成功"",
                ""data"": null
            }";
        }

        private string SimulateSetInputSourceResponse()
        {
            return @"{
                ""status"": 0,
                ""message"": ""输入源设置成功"",
                ""data"": null
            }";
        }

        private string SimulateInputSourceListResponse()
        {
            return @"{
                ""status"": 0,
                ""message"": ""获取输入源列表成功"",
                ""data"": [
                    {
                        ""id"": 1,
                        ""name"": ""HDMI-1"",
                        ""type"": ""HDMI"",
                        ""isOnline"": true,
                        ""resolution"": ""1920x1080"",
                        ""status"": ""正常""
                    },
                    {
                        ""id"": 2,
                        ""name"": ""DVI-1"",
                        ""type"": ""DVI"",
                        ""isOnline"": true,
                        ""resolution"": ""1920x1080"",
                        ""status"": ""正常""
                    },
                    {
                        ""id"": 3,
                        ""name"": ""VGA-1"",
                        ""type"": ""VGA"",
                        ""isOnline"": false,
                        ""resolution"": ""1024x768"",
                        ""status"": ""离线""
                    },
                    {
                        ""id"": 4,
                        ""name"": ""网络流-1"",
                        ""type"": ""Network"",
                        ""isOnline"": true,
                        ""resolution"": ""1920x1080"",
                        ""status"": ""正常""
                    }
                ]
            }";
        }

        private string SimulatePresetListResponse()
        {
            return @"{
                ""status"": 0,
                ""message"": ""获取预设场景列表成功"",
                ""data"": [
                    {
                        ""id"": 1,
                        ""name"": ""实验投屏场景"",
                        ""description"": ""适用于实验室多屏投屏展示"",
                        ""layers"": [
                            {
                                ""layerId"": 1,
                                ""x"": 0,
                                ""y"": 0,
                                ""width"": 3840,
                                ""height"": 2160,
                                ""inputSourceId"": 1,
                                ""zIndex"": 1,
                                ""visible"": true
                            },
                            {
                                ""layerId"": 2,
                                ""x"": 3840,
                                ""y"": 0,
                                ""width"": 3840,
                                ""height"": 2160,
                                ""inputSourceId"": 2,
                                ""zIndex"": 1,
                                ""visible"": true
                            }
                        ]
                    },
                    {
                        ""id"": 2,
                        ""name"": ""演讲场景"",
                        ""description"": ""适用于会议演讲展示"",
                        ""layers"": [
                            {
                                ""layerId"": 1,
                                ""x"": 1920,
                                ""y"": 540,
                                ""width"": 3840,
                                ""height"": 2160,
                                ""inputSourceId"": 1,
                                ""zIndex"": 1,
                                ""visible"": true
                            }
                        ]
                    }
                ]
            }";
        }

        private string SimulateApplyPresetResponse()
        {
            return @"{
                ""status"": 0,
                ""message"": ""预设场景应用成功"",
                ""data"": null
            }";
        }

        private string SimulateDeviceStatusResponse()
        {
            return @"{
                ""status"": 0,
                ""message"": ""获取设备状态成功"",
                ""data"": {
                    ""isOnline"": true,
                    ""temperature"": ""45°C"",
                    ""resolution"": ""7680x4320"",
                    ""layerCount"": 3,
                    ""version"": ""v2.1.0""
                }
            }";
        }

        private string SimulateGenericSuccessResponse()
        {
            return @"{
                ""status"": 0,
                ""message"": ""操作成功"",
                ""data"": null
            }";
        }

        private string SimulateErrorResponse(string endpoint)
        {
            var errorMessages = new Dictionary<string, string>
            {
                ["/api/user/login"] = "用户名或密码错误",
                ["/api/layer/writeWindow"] = "图层移动失败，设备忙碌",
                ["/api/layer/delete"] = "图层删除失败，图层不存在",
                ["/api/layer/create"] = "图层创建失败，超出最大图层数",
                ["/api/layer/writeZIndex"] = "图层层级设置失败",
                ["/api/layer/setInputSource"] = "输入源设置失败，信号源离线",
                ["/api/input/readInputOnlineList"] = "获取输入源失败，设备通信异常",
                ["/api/preset/readPresetList"] = "获取预设场景失败",
                ["/api/preset/apply"] = "应用预设场景失败，场景配置错误",
                ["/api/device/status"] = "获取设备状态失败，设备离线"
            };

            var message = errorMessages.ContainsKey(endpoint)
                ? errorMessages[endpoint]
                : "操作失败，未知错误";

            return $@"{{
                ""status"": -1,
                ""message"": ""{message}"",
                ""data"": null
            }}";
        }
    }
}