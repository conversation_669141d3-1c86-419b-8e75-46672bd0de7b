using System.Net;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using Newtonsoft.Json;

namespace API
{
    public class DeviceApiClient
    {
        private readonly string _baseUrl = "http://192.168.0.10";
        private readonly CookieContainer _cookies = new CookieContainer();

        public async Task<string> PostAsync(string endpoint, object data)
        {
            using (var handler = new HttpClientHandler { CookieContainer = _cookies })
            using (var client = new HttpClient(handler))
            {
                var json = JsonConvert.SerializeObject(data);
                var content = new StringContent(json, Encoding.UTF8, "application/json");
                var response = await client.PostAsync($"{_baseUrl}{endpoint}", content);
                return await response.Content.ReadAsStringAsync();
            }
        }
    }
} 