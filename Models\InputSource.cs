namespace API.Models
{
    /// <summary>
    /// 输入源信息
    /// </summary>
    public class InputSource
    {
        public int id { get; set; }
        public string name { get; set; }
        public string type { get; set; }
        public bool isOnline { get; set; }
        public string resolution { get; set; }
        public string status { get; set; }
    }

    /// <summary>
    /// 输入源列表响应
    /// </summary>
    public class InputSourceListResponse
    {
        public int status { get; set; }
        public string message { get; set; }
        public InputSource[] data { get; set; }
    }
}
