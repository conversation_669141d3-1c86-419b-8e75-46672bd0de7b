# LED拼接屏控制系统模拟器

## 项目概述
这是一个基于C# WPF开发的LED拼接屏控制系统模拟器，用于模拟与LED拼接屏设备的API交互。

## 功能特性

### 1. 设备连接管理
- 设备登录认证
- 连接状态显示
- 实时状态监控

### 2. 图层控制
- **创建图层**：动态创建新的显示图层
- **拖拽移动**：通过鼠标拖拽移动图层位置
- **关闭图层**：删除指定图层
- **图层置顶**：调整图层显示优先级

### 3. 预设场景管理
- **实验投屏场景**：适用于实验室投屏展示
- **演讲场景**：适用于会议演讲
- **监控场景**：适用于监控显示

### 4. 信号源管理
- 获取在线信号源列表
- 为图层分配信号源
- 实时刷新信号源状态

### 5. 可视化界面
- 7680x4320分辨率模拟显示
- 网格线辅助定位
- 实时坐标显示
- 状态栏信息

## 技术架构

### 分层设计
```
UI层 (MainWindow.xaml)
    ↓
业务层 (DeviceController.cs)
    ↓
通信层 (DeviceApiClient.cs)
    ↓
数据层 (Models/*.cs)
```

### 核心类说明

#### DeviceApiClient
- 负责HTTP通信
- 管理Cookie会话
- 封装POST请求

#### DeviceController
- 业务逻辑控制
- API调用封装
- 错误处理

#### 数据模型
- `LoginRequest`: 登录请求
- `LayerCommand`: 图层操作命令
- `InputSource`: 输入源信息
- `PresetScene`: 预设场景配置

## 使用说明

### 1. 启动应用
运行程序后，界面显示未连接状态。

### 2. 设备登录
点击"登录设备"按钮，系统将尝试连接到配置的设备IP地址。

### 3. 图层操作
- **创建图层**：点击"新建窗口"按钮
- **移动图层**：直接拖拽图层到目标位置
- **关闭图层**：选择图层后点击"关闭窗口"
- **置顶图层**：选择图层后点击"置顶"

### 4. 场景切换
点击预设场景按钮快速切换到预定义的显示布局。

### 5. 信号源管理
- 点击"刷新信号源"获取最新信号源列表
- 选择图层和信号源后点击"应用到图层"

## 配置说明

### 设备IP地址
在 `DeviceApiClient.cs` 中修改 `_baseUrl` 变量：
```csharp
private readonly string _baseUrl = "http://************";
```

### 登录凭据
在 `LoginRequest.cs` 中修改默认用户名和密码：
```csharp
public string username { get; set; } = "OlEjflmqZcU=";
public string password { get; set; } = "OlEjflmqZcU=";
```

## API接口映射

| 功能 | API端点 | 实现方法 |
|------|---------|----------|
| 用户登录 | `/api/user/login` | `LoginAsync()` |
| 图层移动 | `/api/layer/writeWindow` | `MoveResizeLayer()` |
| 图层删除 | `/api/layer/delete` | `CloseLayer()` |
| 图层创建 | `/api/layer/create` | `CreateLayer()` |
| 图层置顶 | `/api/layer/writeZIndex` | `SetLayerZIndex()` |
| 输入源列表 | `/api/input/readInputOnlineList` | `GetInputSourcesAsync()` |
| 预设场景 | `/api/preset/readPresetList` | `GetPresetScenesAsync()` |

## 坐标系统

### 设备坐标系
- 分辨率：7680 x 4320
- 原点：左上角 (0, 0)
- 单位：像素

### UI坐标转换
```csharp
// UI坐标转设备坐标
int deviceX = (int)(uiX / DisplayCanvas.ActualWidth * DEVICE_WIDTH);
int deviceY = (int)(uiY / DisplayCanvas.ActualHeight * DEVICE_HEIGHT);

// 设备坐标转UI坐标
double uiX = deviceX * DisplayCanvas.ActualWidth / DEVICE_WIDTH;
double uiY = deviceY * DisplayCanvas.ActualHeight / DEVICE_HEIGHT;
```

## 错误处理

### 网络异常
- 连接超时自动重试
- 显示详细错误信息
- 状态栏实时反馈

### 业务异常
- API响应状态码检查
- 用户友好的错误提示
- 操作失败回滚

## 扩展建议

### 1. 增强功能
- 图层属性编辑器（翻转、旋转、透明度）
- 批量图层操作
- 场景保存和导入
- 操作历史记录

### 2. 性能优化
- 图层虚拟化显示
- 异步操作队列
- 内存使用优化

### 3. 用户体验
- 快捷键支持
- 右键菜单
- 拖拽创建图层
- 多选操作

## 依赖项
- .NET Framework 4.5.2+
- Newtonsoft.Json 13.0.3
- WPF框架

## 注意事项
1. 确保设备IP地址正确配置
2. 网络连接稳定
3. 设备API版本兼容性
4. 坐标系统精度处理
