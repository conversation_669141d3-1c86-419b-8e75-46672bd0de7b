using System.Threading.Tasks;
using API.Models;

namespace API
{
    public class DeviceController
    {
        private readonly DeviceApiClient _client = new DeviceApiClient();

        public async Task<bool> LoginAsync()
        {
            var response = await _client.PostAsync("/api/user/login", new LoginRequest());
            // 解析响应判断登录成功
            return response.Contains("\"status\":0");
        }

        public async Task MoveResizeLayer(int layerId, int x, int y, int width, int height)
        {
            var command = new LayerCommand {
                layerId = layerId,
                x = x,
                y = y,
                width = width,
                height = height
            };
            await _client.PostAsync("/api/layer/writeWindow", command);
        }

        public async Task CloseLayer(int layerId)
        {
            await _client.PostAsync("/api/layer/delete", new {
                screenId = 0,
                deviceId = 0,
                layerId = layerId
            });
        }
    }
} 