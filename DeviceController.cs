using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using API.Models;
using Newtonsoft.Json;

namespace API
{
    public class DeviceController
    {
        private readonly DeviceApiClient _client = new DeviceApiClient();
        private bool _isLoggedIn = false;

        /// <summary>
        /// 设置模拟模式
        /// </summary>
        public void SetSimulationMode(bool simulationMode)
        {
            _client.SimulationMode = simulationMode;
            // 切换模式时重置登录状态
            _isLoggedIn = false;
        }

        /// <summary>
        /// 获取当前是否为模拟模式
        /// </summary>
        public bool IsSimulationMode => _client.SimulationMode;

        /// <summary>
        /// 设置错误模拟
        /// </summary>
        public void SetSimulateErrors(bool simulateErrors)
        {
            _client.SimulateErrors = simulateErrors;
        }

        /// <summary>
        /// 登录设备
        /// </summary>
        public async Task<LoginResponse> LoginAsync()
        {
            try
            {
                var response = await _client.PostAsync("/api/user/login", new LoginRequest());
                var loginResponse = JsonConvert.DeserializeObject<LoginResponse>(response);
                _isLoggedIn = loginResponse.status == 0;
                return loginResponse;
            }
            catch (Exception ex)
            {
                return new LoginResponse { status = -1, message = $"登录失败: {ex.Message}" };
            }
        }

        /// <summary>
        /// 检查登录状态
        /// </summary>
        public bool IsLoggedIn => _isLoggedIn;

        /// <summary>
        /// 移动和调整图层大小
        /// </summary>
        public async Task<ApiResponse> MoveResizeLayer(int layerId, int x, int y, int width, int height)
        {
            try
            {
                var command = new LayerCommand {
                    layerId = layerId,
                    x = x,
                    y = y,
                    width = width,
                    height = height
                };
                var response = await _client.PostAsync("/api/layer/writeWindow", command);
                return JsonConvert.DeserializeObject<ApiResponse>(response);
            }
            catch (Exception ex)
            {
                return new ApiResponse { status = -1, message = $"移动图层失败: {ex.Message}" };
            }
        }

        /// <summary>
        /// 关闭图层
        /// </summary>
        public async Task<ApiResponse> CloseLayer(int layerId)
        {
            try
            {
                var response = await _client.PostAsync("/api/layer/delete", new {
                    screenId = 0,
                    deviceId = 0,
                    layerId = layerId
                });
                return JsonConvert.DeserializeObject<ApiResponse>(response);
            }
            catch (Exception ex)
            {
                return new ApiResponse { status = -1, message = $"关闭图层失败: {ex.Message}" };
            }
        }

        /// <summary>
        /// 创建新图层
        /// </summary>
        public async Task<ApiResponse> CreateLayer(int layerId, int x, int y, int width, int height, int inputSourceId = 1)
        {
            try
            {
                var command = new {
                    screenId = 0,
                    deviceId = 0,
                    layerId = layerId,
                    x = x,
                    y = y,
                    width = width,
                    height = height,
                    inputSourceId = inputSourceId,
                    visible = true
                };
                var response = await _client.PostAsync("/api/layer/create", command);
                return JsonConvert.DeserializeObject<ApiResponse>(response);
            }
            catch (Exception ex)
            {
                return new ApiResponse { status = -1, message = $"创建图层失败: {ex.Message}" };
            }
        }

        /// <summary>
        /// 设置图层层级
        /// </summary>
        public async Task<ApiResponse> SetLayerZIndex(int layerId, int zIndex)
        {
            try
            {
                var command = new {
                    screenId = 0,
                    deviceId = 0,
                    layerId = layerId,
                    zIndex = zIndex
                };
                var response = await _client.PostAsync("/api/layer/writeZIndex", command);
                return JsonConvert.DeserializeObject<ApiResponse>(response);
            }
            catch (Exception ex)
            {
                return new ApiResponse { status = -1, message = $"设置图层层级失败: {ex.Message}" };
            }
        }

        /// <summary>
        /// 获取在线输入源列表
        /// </summary>
        public async Task<InputSourceListResponse> GetInputSourcesAsync()
        {
            try
            {
                var response = await _client.PostAsync("/api/input/readInputOnlineList", new {
                    screenId = 0,
                    deviceId = 0
                });
                return JsonConvert.DeserializeObject<InputSourceListResponse>(response);
            }
            catch (Exception ex)
            {
                return new InputSourceListResponse {
                    status = -1,
                    message = $"获取输入源失败: {ex.Message}",
                    data = new InputSource[0]
                };
            }
        }

        /// <summary>
        /// 获取预设场景列表
        /// </summary>
        public async Task<PresetSceneListResponse> GetPresetScenesAsync()
        {
            try
            {
                var response = await _client.PostAsync("/api/preset/readPresetList", new {
                    screenId = 0,
                    deviceId = 0
                });
                return JsonConvert.DeserializeObject<PresetSceneListResponse>(response);
            }
            catch (Exception ex)
            {
                return new PresetSceneListResponse {
                    status = -1,
                    message = $"获取预设场景失败: {ex.Message}",
                    data = new PresetScene[0]
                };
            }
        }

        /// <summary>
        /// 应用预设场景
        /// </summary>
        public async Task<ApiResponse> ApplyPresetScene(int presetId)
        {
            try
            {
                var request = new ApplyPresetRequest { presetId = presetId };
                var response = await _client.PostAsync("/api/preset/apply", request);
                return JsonConvert.DeserializeObject<ApiResponse>(response);
            }
            catch (Exception ex)
            {
                return new ApiResponse { status = -1, message = $"应用预设场景失败: {ex.Message}" };
            }
        }

        /// <summary>
        /// 设置图层输入源
        /// </summary>
        public async Task<ApiResponse> SetLayerInputSource(int layerId, int inputSourceId)
        {
            try
            {
                var command = new {
                    screenId = 0,
                    deviceId = 0,
                    layerId = layerId,
                    inputSourceId = inputSourceId
                };
                var response = await _client.PostAsync("/api/layer/setInputSource", command);
                return JsonConvert.DeserializeObject<ApiResponse>(response);
            }
            catch (Exception ex)
            {
                return new ApiResponse { status = -1, message = $"设置图层输入源失败: {ex.Message}" };
            }
        }

        /// <summary>
        /// 获取设备状态
        /// </summary>
        public async Task<DeviceStatusResponse> GetDeviceStatusAsync()
        {
            try
            {
                var response = await _client.PostAsync("/api/device/status", new {
                    screenId = 0,
                    deviceId = 0
                });
                return JsonConvert.DeserializeObject<DeviceStatusResponse>(response);
            }
            catch (Exception ex)
            {
                return new DeviceStatusResponse {
                    status = -1,
                    message = $"获取设备状态失败: {ex.Message}",
                    data = new DeviceStatus()
                };
            }
        }
    }
}