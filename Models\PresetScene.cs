namespace API.Models
{
    /// <summary>
    /// 预设场景信息
    /// </summary>
    public class PresetScene
    {
        public int id { get; set; }
        public string name { get; set; }
        public string description { get; set; }
        public LayerConfig[] layers { get; set; }
    }

    /// <summary>
    /// 图层配置
    /// </summary>
    public class LayerConfig
    {
        public int layerId { get; set; }
        public int x { get; set; }
        public int y { get; set; }
        public int width { get; set; }
        public int height { get; set; }
        public int inputSourceId { get; set; }
        public int zIndex { get; set; }
        public bool visible { get; set; }
    }

    /// <summary>
    /// 预设场景列表响应
    /// </summary>
    public class PresetSceneListResponse
    {
        public int status { get; set; }
        public string message { get; set; }
        public PresetScene[] data { get; set; }
    }

    /// <summary>
    /// 应用预设场景请求
    /// </summary>
    public class ApplyPresetRequest
    {
        public int screenId { get; set; } = 0;
        public int deviceId { get; set; } = 0;
        public int presetId { get; set; }
    }
}
