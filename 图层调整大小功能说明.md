# 图层调整大小功能说明

## 🎯 新增功能概述

我已经为LED拼接屏控制系统添加了完整的图层调整大小功能，包括：

1. **图层选中机制**：点击图层进行选中，显示黄色边框
2. **边缘检测**：鼠标悬停在图层边缘时显示调整大小光标
3. **8方向调整**：支持上下左右及四个角的调整大小
4. **实时预览**：调整过程中实时显示新的大小
5. **后端同步**：调整完成后自动发送writeWindow命令到后端

## 🔧 核心功能实现

### 1. 图层选中机制

<augment_code_snippet path="MainWindow.xaml.cs" mode="EXCERPT">
```csharp
private void SelectLayer(Border layer)
{
    // 取消之前选中的图层
    if (_selectedLayer != null)
    {
        _selectedLayer.BorderBrush = Brushes.White;
        _selectedLayer.BorderThickness = new Thickness(2);
    }
    
    // 选中新图层
    _selectedLayer = layer;
    if (_selectedLayer != null)
    {
        _selectedLayer.BorderBrush = Brushes.Yellow;  // 黄色边框表示选中
        _selectedLayer.BorderThickness = new Thickness(3);
    }
}
```
</augment_code_snippet>

### 2. 调整大小方向检测

<augment_code_snippet path="MainWindow.xaml.cs" mode="EXCERPT">
```csharp
private string GetResizeDirection(Border layer, Point position)
{
    const double edgeThickness = 10; // 边缘检测厚度
    
    bool nearLeft = position.X <= edgeThickness;
    bool nearRight = position.X >= layer.Width - edgeThickness;
    bool nearTop = position.Y <= edgeThickness;
    bool nearBottom = position.Y >= layer.Height - edgeThickness;
    
    // 返回8个方向：N, S, E, W, NE, NW, SE, SW
    if (nearTop && nearLeft) return "NW";
    if (nearTop && nearRight) return "NE";
    // ... 其他方向
}
```
</augment_code_snippet>

### 3. 光标样式更新

根据鼠标位置自动更新光标样式：
- **上下边缘**：`SizeNS` (↕)
- **左右边缘**：`SizeWE` (↔)
- **左上/右下角**：`SizeNWSE` (↖↘)
- **右上/左下角**：`SizeNESW` (↗↙)
- **中心区域**：`Hand` (✋)

### 4. 实时调整大小

<augment_code_snippet path="MainWindow.xaml.cs" mode="EXCERPT">
```csharp
private void ResizeLayer(Border layer, Point currentPoint)
{
    var deltaX = currentPoint.X - _resizeStartPoint.X;
    var deltaY = currentPoint.Y - _resizeStartPoint.Y;
    
    // 根据调整方向计算新的位置和大小
    switch (_resizeDirection)
    {
        case "SE": // 右下角
            newWidth += deltaX;
            newHeight += deltaY;
            break;
        // ... 其他方向的处理
    }
    
    // 应用限制和边界检查
    // 更新图层位置和大小
}
```
</augment_code_snippet>

### 5. 后端命令发送

<augment_code_snippet path="MainWindow.xaml.cs" mode="EXCERPT">
```csharp
private async Task SendLayerUpdateToBackend(Border layer)
{
    var layerId = (int)layer.Tag;
    
    // UI坐标转换为设备坐标
    int deviceX = (int)(Canvas.GetLeft(layer) / scaleX);
    int deviceY = (int)(Canvas.GetTop(layer) / scaleY);
    int deviceW = (int)(layer.Width / scaleX);
    int deviceH = (int)(layer.Height / scaleY);
    
    // 发送writeWindow命令
    var response = await _controller.MoveResizeLayer(layerId, deviceX, deviceY, deviceW, deviceH);
}
```
</augment_code_snippet>

## 🎮 用户操作指南

### 基本操作流程

1. **选中图层**
   - 点击任意图层
   - 图层边框变为黄色，表示已选中
   - 状态栏显示"已选中图层 X"

2. **调整大小**
   - 将鼠标移动到选中图层的边缘
   - 光标自动变为调整大小样式
   - 按住鼠标左键拖拽调整大小
   - 松开鼠标完成调整

3. **取消选中**
   - 点击Canvas空白区域
   - 图层边框恢复为白色
   - 状态栏显示"已取消图层选中"

### 调整大小操作

| 鼠标位置 | 光标样式 | 调整效果 |
|----------|----------|----------|
| 上边缘 | ↕ | 向上/下调整高度 |
| 下边缘 | ↕ | 向上/下调整高度 |
| 左边缘 | ↔ | 向左/右调整宽度 |
| 右边缘 | ↔ | 向左/右调整宽度 |
| 左上角 | ↖↘ | 同时调整宽度和高度 |
| 右上角 | ↗↙ | 同时调整宽度和高度 |
| 左下角 | ↗↙ | 同时调整宽度和高度 |
| 右下角 | ↖↘ | 同时调整宽度和高度 |

## 🔒 安全限制

### 1. 最小尺寸限制
- 图层最小宽度：50像素
- 图层最小高度：50像素
- 防止图层缩小到不可见

### 2. 边界限制
- 图层不能超出Canvas边界
- 自动调整超出边界的尺寸
- 保持图层完全在可视区域内

### 3. 坐标精度
- UI坐标精确转换为设备坐标
- 支持7680x4320高分辨率
- 保持坐标系统一致性

## 📊 技术实现细节

### 状态管理
```csharp
// 新增的状态变量
private Border _selectedLayer;        // 当前选中的图层
private bool _isResizing = false;     // 是否正在调整大小
private string _resizeDirection = ""; // 调整方向
private Point _resizeStartPoint;      // 调整起始点
private Rect _originalBounds;         // 原始边界
```

### 事件处理
- **MouseDown**：检测点击位置，判断是拖拽还是调整大小
- **MouseMove**：实时更新图层位置或大小，更新光标样式
- **MouseUp**：完成操作，发送后端命令

### 坐标转换
```csharp
// UI坐标 → 设备坐标
double scaleX = DisplayCanvas.ActualWidth / DEVICE_WIDTH;
double scaleY = DisplayCanvas.ActualHeight / DEVICE_HEIGHT;
int deviceX = (int)(uiX / scaleX);
int deviceY = (int)(uiY / scaleY);
```

## 🧪 测试建议

### 功能测试
1. **基本调整**
   - 创建图层并选中
   - 测试8个方向的调整大小
   - 验证光标样式正确

2. **边界测试**
   - 调整到最小尺寸
   - 调整到Canvas边界
   - 验证限制生效

3. **后端同步测试**
   - 调整大小后检查状态栏反馈
   - 验证设备坐标转换正确
   - 测试模拟模式下的响应

### 压力测试
1. **快速操作**
   - 连续快速调整多个图层
   - 验证状态管理正确

2. **大量图层**
   - 创建多个图层
   - 测试选中切换
   - 验证性能表现

## 🎯 使用效果

### 视觉反馈
- ✅ **选中状态**：黄色边框，3像素厚度
- ✅ **光标提示**：8种调整大小光标
- ✅ **实时预览**：拖拽过程中实时显示新尺寸
- ✅ **状态反馈**：状态栏显示操作结果

### 操作体验
- ✅ **直观操作**：鼠标悬停自动显示调整光标
- ✅ **精确控制**：10像素边缘检测区域
- ✅ **流畅响应**：实时调整无延迟
- ✅ **安全限制**：防止误操作和越界

## 🚀 下一步扩展

### 可能的增强功能
1. **多选调整**：同时调整多个图层大小
2. **比例锁定**：按住Shift键保持宽高比
3. **网格对齐**：调整时自动对齐到网格
4. **快捷键**：键盘快捷键精确调整
5. **撤销重做**：支持操作历史记录

### 性能优化
1. **虚拟化**：大量图层时的性能优化
2. **批量更新**：减少后端API调用频率
3. **缓存机制**：缓存坐标转换结果

现在您可以测试这个新功能了！创建图层后，点击选中，然后将鼠标移动到边缘进行调整大小操作。
