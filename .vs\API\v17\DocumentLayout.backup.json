{"Version": 1, "WorkspaceRootPath": "E:\\work\\Csharp\\test\\1.API\\API\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|e:\\work\\csharp\\test\\1.api\\api\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|solutionrelative:mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|e:\\work\\csharp\\test\\1.api\\api\\deviceapiclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|solutionrelative:deviceapiclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|e:\\work\\csharp\\test\\1.api\\api\\mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}", "RelativeMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|solutionrelative:mainwindow.xaml||{F11ACC28-31D1-4C80-A34B-F4E09D3D753C}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 3, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 1, "Title": "DeviceApiClient.cs", "DocumentMoniker": "E:\\work\\Csharp\\test\\1.API\\API\\DeviceApiClient.cs", "RelativeDocumentMoniker": "DeviceApiClient.cs", "ToolTip": "E:\\work\\Csharp\\test\\1.API\\API\\DeviceApiClient.cs", "RelativeToolTip": "DeviceApiClient.cs", "ViewState": "AgIAABUBAAAAAAAAAAAowCEBAAAkAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T03:46:48.031Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "MainWindow.xaml", "DocumentMoniker": "E:\\work\\Csharp\\test\\1.API\\API\\MainWindow.xaml", "RelativeDocumentMoniker": "MainWindow.xaml", "ToolTip": "E:\\work\\Csharp\\test\\1.API\\API\\MainWindow.xaml", "RelativeToolTip": "MainWindow.xaml", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.003549|", "WhenOpened": "2025-07-07T03:44:28.218Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "E:\\work\\Csharp\\test\\1.API\\API\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "MainWindow.xaml.cs", "ToolTip": "E:\\work\\Csharp\\test\\1.API\\API\\MainWindow.xaml.cs*", "RelativeToolTip": "MainWindow.xaml.cs*", "ViewState": "AgIAAK0BAAAAAAAAAAAowLsBAAApAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T03:32:07.257Z", "EditorCaption": ""}]}]}]}