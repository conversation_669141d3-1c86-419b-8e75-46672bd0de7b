{"Version": 1, "WorkspaceRootPath": "E:\\work\\Csharp\\test\\1.API\\API\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|e:\\work\\csharp\\test\\1.api\\api\\deviceapiclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|solutionrelative:deviceapiclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "DeviceApiClient.cs", "DocumentMoniker": "E:\\work\\Csharp\\test\\1.API\\API\\DeviceApiClient.cs", "RelativeDocumentMoniker": "DeviceApiClient.cs", "ToolTip": "E:\\work\\Csharp\\test\\1.API\\API\\DeviceApiClient.cs", "RelativeToolTip": "DeviceApiClient.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwUAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T02:24:49.602Z", "EditorCaption": ""}]}]}]}