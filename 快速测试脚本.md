# 快速测试脚本 - 5分钟功能验证

## 🚀 快速启动测试

### 第一步：启动应用（30秒）
1. 打开Visual Studio或VS Code
2. 编译并运行项目
3. 确认界面正常显示
4. 检查标题栏显示"[模拟模式]"

### 第二步：基础连接测试（1分钟）
```
操作序列：
1. 点击"登录设备" → 等待2-3秒
2. 观察连接状态变为"已连接"（绿色）
3. 确认状态栏显示"登录成功"
4. 验证按钮变为"已登录"并禁用

✅ 通过标准：连接状态正确，无异常提示
```

### 第三步：图层操作测试（2分钟）
```
操作序列：
1. 点击"新建窗口" → 观察蓝色图层出现
2. 再次点击"新建窗口" → 观察红色图层出现
3. 拖拽蓝色图层到右上角
4. 拖拽红色图层到左下角
5. 选择图层1，点击"关闭窗口"
6. 观察蓝色图层消失

✅ 通过标准：图层创建、移动、删除正常
```

### 第四步：信号源测试（1分钟）
```
操作序列：
1. 点击"刷新信号源"
2. 观察信号源下拉框出现4个选项
3. 选择"HDMI-1 (HDMI)"
4. 选择剩余的图层
5. 点击"应用到图层"
6. 观察状态栏显示成功信息

✅ 通过标准：信号源列表正确，应用成功
```

### 第五步：预设场景测试（30秒）
```
操作序列：
1. 点击"实验投屏" → 观察状态栏反馈
2. 点击"演讲场景" → 观察状态栏反馈
3. 点击"监控场景" → 观察状态栏反馈

✅ 通过标准：所有场景按钮响应正常
```

### 第六步：界面交互测试（30秒）
```
操作序列：
1. 在显示区域移动鼠标
2. 观察右上角坐标实时变化
3. 验证坐标范围（0-7679, 0-4319）
4. 观察网格线显示正常

✅ 通过标准：坐标显示准确，界面响应流畅
```

## 🎯 一键测试序列

### 完整功能演示（按顺序执行）
```
1. 启动应用
   ↓
2. 确认模拟模式开启
   ↓
3. 点击"登录设备"
   ↓
4. 连续点击3次"新建窗口"
   ↓
5. 拖拽每个图层到不同位置
   ↓
6. 点击"刷新信号源"
   ↓
7. 为每个图层设置不同信号源
   ↓
8. 依次测试三个预设场景
   ↓
9. 选择一个图层点击"置顶"
   ↓
10. 选择一个图层点击"关闭窗口"
```

## 🔍 关键检查点

### 必须验证的状态变化
- [ ] 连接状态：未连接 → 已连接
- [ ] 按钮状态：登录设备 → 已登录
- [ ] 图层计数：0 → 3 → 2
- [ ] 坐标显示：实时更新
- [ ] 状态信息：操作反馈及时

### 必须验证的功能响应
- [ ] 所有按钮点击有反应
- [ ] 拖拽操作流畅无卡顿
- [ ] 下拉框数据正确填充
- [ ] 错误提示友好明确

## 🚨 异常情况测试

### 未登录操作测试
```
操作序列：
1. 取消勾选"模拟模式"
2. 不点击登录，直接点击"新建窗口"
3. 观察是否有"请先登录设备"提示

✅ 通过标准：有友好的错误提示
```

### 无效操作测试
```
操作序列：
1. 不选择图层，点击"关闭窗口"
2. 不选择信号源，点击"应用到图层"
3. 观察错误提示

✅ 通过标准：有明确的操作指导
```

## 📊 性能基准测试

### 响应时间测试
- 登录响应：< 1秒
- 图层创建：< 0.5秒
- 拖拽响应：< 0.1秒
- 信号源刷新：< 1秒

### 资源使用测试
- 内存使用：< 100MB
- CPU使用：< 5%（空闲时）
- 界面流畅度：60FPS

## 🔄 模式切换测试

### 模拟模式 → 真实设备模式
```
操作序列：
1. 在模拟模式下完成登录
2. 取消勾选"模拟模式"
3. 观察标题变为"[真实设备模式]"
4. 观察连接状态重置为"未连接"
5. 观察状态栏提示"请重新登录"

✅ 通过标准：模式切换正确，状态重置
```

### 真实设备模式 → 模拟模式
```
操作序列：
1. 在真实设备模式下
2. 勾选"模拟模式"
3. 观察标题变为"[模拟模式]"
4. 点击登录测试模拟功能

✅ 通过标准：切换回模拟模式正常
```

## 📝 测试结果记录

### 快速测试清单
```
□ 应用启动正常
□ 模拟模式工作正常
□ 登录功能正常
□ 图层创建正常
□ 图层拖拽正常
□ 图层删除正常
□ 图层置顶正常
□ 信号源刷新正常
□ 信号源应用正常
□ 预设场景正常
□ 坐标显示正常
□ 状态反馈正常
□ 错误处理正常
□ 模式切换正常
□ 性能表现良好
```

### 发现的问题
```
1. [问题描述] - [严重程度] - [解决方案]
2. [问题描述] - [严重程度] - [解决方案]
3. [问题描述] - [严重程度] - [解决方案]
```

### 测试结论
```
□ 通过 - 所有功能正常，可以进行真实设备测试
□ 部分通过 - 有小问题但不影响主要功能
□ 不通过 - 有严重问题需要修复
```

## 🎉 测试完成后的下一步

### 如果测试通过：
1. 准备真实设备环境
2. 配置设备IP地址
3. 切换到真实设备模式
4. 进行真实设备联调

### 如果发现问题：
1. 记录详细的问题描述
2. 尝试重现问题步骤
3. 检查控制台错误信息
4. 联系开发团队修复

## 💡 测试技巧

### 高效测试方法：
1. **批量操作**：一次创建多个图层测试
2. **边界测试**：拖拽到屏幕边缘测试
3. **快速操作**：连续快速点击测试稳定性
4. **长时间运行**：让程序运行一段时间测试内存泄漏

### 问题定位技巧：
1. **分步测试**：逐个功能点测试
2. **重现问题**：多次执行相同操作
3. **环境检查**：确认.NET版本和依赖项
4. **日志查看**：观察控制台输出信息

通过这个快速测试脚本，您可以在5分钟内验证系统的核心功能，确保在没有真实设备的情况下代码的正确性和稳定性。
