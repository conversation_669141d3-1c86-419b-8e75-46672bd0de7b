﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Shapes;
using System.Windows.Threading;
using API.Models;

namespace API
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private DeviceController _controller = new DeviceController();
        private UIElement _draggingLayer;
        private Point _dragOffset;
        private List<Border> _layers = new List<Border>();
        private List<InputSource> _inputSources = new List<InputSource>();
        private int _nextLayerId = 1;

        // 设备分辨率常量
        private const int DEVICE_WIDTH = 7680;
        private const int DEVICE_HEIGHT = 4320;

        public MainWindow()
        {
            InitializeComponent();
            InitializeUI();
        }

        private void InitializeUI()
        {
            // 绘制网格线
            DrawGrid();

            // 初始化状态
            UpdateConnectionStatus(false);
            UpdateLayerCount();

            // 添加坐标显示事件
            DisplayCanvas.MouseMove += DisplayCanvas_MouseMove;
        }

        private void DrawGrid()
        {
            GridCanvas.Children.Clear();

            // 绘制网格线（每500像素一条线）
            int gridSize = 500;
            var brush = new SolidColorBrush(Color.FromArgb(50, 255, 255, 255));

            // 垂直线
            for (int x = 0; x < DEVICE_WIDTH; x += gridSize)
            {
                var line = new Line
                {
                    X1 = x * (DisplayCanvas.ActualWidth / DEVICE_WIDTH),
                    Y1 = 0,
                    X2 = x * (DisplayCanvas.ActualWidth / DEVICE_WIDTH),
                    Y2 = DisplayCanvas.ActualHeight,
                    Stroke = brush,
                    StrokeThickness = 1
                };
                GridCanvas.Children.Add(line);
            }

            // 水平线
            for (int y = 0; y < DEVICE_HEIGHT; y += gridSize)
            {
                var line = new Line
                {
                    X1 = 0,
                    Y1 = y * (DisplayCanvas.ActualHeight / DEVICE_HEIGHT),
                    X2 = DisplayCanvas.ActualWidth,
                    Y2 = y * (DisplayCanvas.ActualHeight / DEVICE_HEIGHT),
                    Stroke = brush,
                    StrokeThickness = 1
                };
                GridCanvas.Children.Add(line);
            }
        }

        private void DisplayCanvas_MouseMove(object sender, MouseEventArgs e)
        {
            // 更新坐标显示
            var position = e.GetPosition(DisplayCanvas);
            var deviceX = (int)(position.X / DisplayCanvas.ActualWidth * DEVICE_WIDTH);
            var deviceY = (int)(position.Y / DisplayCanvas.ActualHeight * DEVICE_HEIGHT);
            CoordinateDisplay.Text = $"坐标: ({deviceX}, {deviceY})";
        }

        private void UpdateConnectionStatus(bool isConnected)
        {
            ConnectionStatus.Text = isConnected ? "已连接" : "未连接";
            ConnectionStatus.Foreground = isConnected ? Brushes.Green : Brushes.Red;
            LoginButton.Content = isConnected ? "已登录" : "登录设备";
            LoginButton.IsEnabled = !isConnected;
        }

        private void UpdateLayerCount()
        {
            LayerCountText.Text = $"图层数: {_layers.Count}";
        }

        private void UpdateStatus(string message)
        {
            StatusText.Text = message;
        }

        private async void Login_Click(object sender, RoutedEventArgs e)
        {
            UpdateStatus("正在登录...");
            LoginButton.IsEnabled = false;

            try
            {
                var response = await _controller.LoginAsync();
                if (response.status == 0)
                {
                    UpdateConnectionStatus(true);
                    UpdateStatus("登录成功");

                    // 登录成功后刷新输入源列表
                    await RefreshInputSources();
                }
                else
                {
                    UpdateConnectionStatus(false);
                    UpdateStatus($"登录失败: {response.message}");
                    MessageBox.Show($"登录失败: {response.message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                UpdateConnectionStatus(false);
                UpdateStatus($"登录异常: {ex.Message}");
                MessageBox.Show($"登录异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                LoginButton.IsEnabled = !_controller.IsLoggedIn;
            }
        }

        private async Task RefreshInputSources()
        {
            try
            {
                var response = await _controller.GetInputSourcesAsync();
                if (response.status == 0)
                {
                    _inputSources = response.data.ToList();
                    InputSourceSelector.Items.Clear();

                    foreach (var source in _inputSources)
                    {
                        InputSourceSelector.Items.Add($"{source.name} ({source.type})");
                    }

                    if (_inputSources.Count > 0)
                    {
                        InputSourceSelector.SelectedIndex = 0;
                    }
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"刷新输入源失败: {ex.Message}");
            }
        }

        private async void CreateLayer_Click(object sender, RoutedEventArgs e)
        {
            //if (!_controller.IsLoggedIn)
            //{
            //    MessageBox.Show("请先登录设备", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            //    return;
            //}

            try
            {
                // 创建新图层，默认位置和大小
                int layerId = _nextLayerId++;
                int x = 100 + (_layers.Count * 50);
                int y = 100 + (_layers.Count * 50);
                int width = 800;
                int height = 600;

                var response = await _controller.CreateLayer(layerId, x, y, width, height);
                if (response.status == 0)
                {
                    CreateLayerUI(layerId, x, y, width, height);
                    UpdateStatus($"图层 {layerId} 创建成功");
                }
                else
                {
                    UpdateStatus($"创建图层失败: {response.message}");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"创建图层异常: {ex.Message}");
            }
        }

        private void CreateLayerUI(int layerId, int deviceX, int deviceY, int deviceWidth, int deviceHeight)
        {
            // 坐标换算：设备坐标转换为UI坐标
            double scaleX = DisplayCanvas.ActualWidth / DEVICE_WIDTH;
            double scaleY = DisplayCanvas.ActualHeight / DEVICE_HEIGHT;

            var layer = new Border
            {
                Name = $"Layer{layerId}",
                Background = GetRandomBrush(),
                BorderBrush = Brushes.White,
                BorderThickness = new Thickness(2),
                Width = deviceWidth * scaleX,
                Height = deviceHeight * scaleY,
                Cursor = Cursors.Hand
            };

            // 添加图层标签
            var textBlock = new TextBlock
            {
                Text = $"图层{layerId}",
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                FontWeight = FontWeights.Bold
            };
            layer.Child = textBlock;

            Canvas.SetLeft(layer, deviceX * scaleX);
            Canvas.SetTop(layer, deviceY * scaleY);
            Canvas.SetZIndex(layer, layerId);

            layer.MouseLeftButtonDown += Layer_MouseDown;
            layer.Tag = layerId; // 存储图层ID

            DisplayCanvas.Children.Add(layer);
            _layers.Add(layer);
            UpdateLayerCount();
        }

        private Brush GetRandomBrush()
        {
            var colors = new[] { Colors.Blue, Colors.Red, Colors.Green, Colors.Orange, Colors.Purple, Colors.Brown };
            var random = new Random();
            var color = colors[random.Next(colors.Length)];
            color.A = 180; // 半透明
            return new SolidColorBrush(color);
        }

        private void Layer_MouseDown(object sender, MouseButtonEventArgs e)
        {
            _draggingLayer = sender as UIElement;
            _dragOffset = e.GetPosition(_draggingLayer);
            _draggingLayer.CaptureMouse();

            // 更新图层选择器
            var layerId = (int)((Border)_draggingLayer).Tag;
            LayerSelector.SelectedIndex = layerId - 1;
        }

        private void Canvas_MouseMove(object sender, MouseEventArgs e)
        {
            if (_draggingLayer != null)
            {
                var canvas = (Canvas)sender;
                Point position = e.GetPosition(canvas);
                Canvas.SetLeft(_draggingLayer, position.X - _dragOffset.X);
                Canvas.SetTop(_draggingLayer, position.Y - _dragOffset.Y);
            }
        }

        private async void Canvas_MouseUp(object sender, MouseButtonEventArgs e)
        {
            if (_draggingLayer != null)
            {
                _draggingLayer.ReleaseMouseCapture();

                var layerId = (int)((Border)_draggingLayer).Tag;

                // 坐标换算：UI坐标转换为设备坐标
                double scaleX = DisplayCanvas.ActualWidth / DEVICE_WIDTH;
                double scaleY = DisplayCanvas.ActualHeight / DEVICE_HEIGHT;

                int deviceX = (int)(Canvas.GetLeft(_draggingLayer) / scaleX);
                int deviceY = (int)(Canvas.GetTop(_draggingLayer) / scaleY);
                int deviceW = (int)(_draggingLayer.RenderSize.Width / scaleX);
                int deviceH = (int)(_draggingLayer.RenderSize.Height / scaleY);

                try
                {
                    var response = await _controller.MoveResizeLayer(layerId, deviceX, deviceY, deviceW, deviceH);
                    if (response.status == 0)
                    {
                        UpdateStatus($"图层 {layerId} 移动成功");
                    }
                    else
                    {
                        UpdateStatus($"移动图层失败: {response.message}");
                    }
                }
                catch (Exception ex)
                {
                    UpdateStatus($"移动图层异常: {ex.Message}");
                }

                _draggingLayer = null;
            }
        }

        private async void CloseLayer_Click(object sender, RoutedEventArgs e)
        {
            if (!_controller.IsLoggedIn)
            {
                MessageBox.Show("请先登录设备", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (LayerSelector.SelectedIndex < 0 || LayerSelector.SelectedIndex >= _layers.Count)
            {
                MessageBox.Show("请选择要关闭的图层", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var layer = _layers[LayerSelector.SelectedIndex];
                var layerId = (int)layer.Tag;

                var response = await _controller.CloseLayer(layerId);
                if (response.status == 0)
                {
                    DisplayCanvas.Children.Remove(layer);
                    _layers.Remove(layer);
                    UpdateLayerCount();
                    UpdateStatus($"图层 {layerId} 关闭成功");
                }
                else
                {
                    UpdateStatus($"关闭图层失败: {response.message}");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"关闭图层异常: {ex.Message}");
            }
        }

        private async void BringToFront_Click(object sender, RoutedEventArgs e)
        {
            if (!_controller.IsLoggedIn)
            {
                MessageBox.Show("请先登录设备", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (LayerSelector.SelectedIndex < 0 || LayerSelector.SelectedIndex >= _layers.Count)
            {
                MessageBox.Show("请选择要置顶的图层", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var layer = _layers[LayerSelector.SelectedIndex];
                var layerId = (int)layer.Tag;

                var response = await _controller.SetLayerZIndex(layerId, 99);
                if (response.status == 0)
                {
                    Canvas.SetZIndex(layer, 99);
                    UpdateStatus($"图层 {layerId} 置顶成功");
                }
                else
                {
                    UpdateStatus($"置顶图层失败: {response.message}");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"置顶图层异常: {ex.Message}");
            }
        }

        // 预设场景事件处理
        private async void ExperimentScene_Click(object sender, RoutedEventArgs e)
        {
            await ApplyPresetScene("实验投屏", 1);
        }

        private async void PresentationScene_Click(object sender, RoutedEventArgs e)
        {
            await ApplyPresetScene("演讲场景", 2);
        }

        private async void MonitorScene_Click(object sender, RoutedEventArgs e)
        {
            await ApplyPresetScene("监控场景", 3);
        }

        private async Task ApplyPresetScene(string sceneName, int presetId)
        {
            if (!_controller.IsLoggedIn)
            {
                MessageBox.Show("请先登录设备", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                UpdateStatus($"正在应用{sceneName}...");
                var response = await _controller.ApplyPresetScene(presetId);
                if (response.status == 0)
                {
                    UpdateStatus($"{sceneName}应用成功");
                    // 这里可以根据需要刷新UI显示
                }
                else
                {
                    UpdateStatus($"应用{sceneName}失败: {response.message}");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"应用{sceneName}异常: {ex.Message}");
            }
        }

        private async void RefreshInputSources_Click(object sender, RoutedEventArgs e)
        {
            if (!_controller.IsLoggedIn)
            {
                MessageBox.Show("请先登录设备", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            UpdateStatus("正在刷新输入源...");
            await RefreshInputSources();
            UpdateStatus("输入源刷新完成");
        }

        private async void ApplyInputToLayer_Click(object sender, RoutedEventArgs e)
        {
            if (!_controller.IsLoggedIn)
            {
                MessageBox.Show("请先登录设备", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (LayerSelector.SelectedIndex < 0 || LayerSelector.SelectedIndex >= _layers.Count)
            {
                MessageBox.Show("请选择要设置的图层", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (InputSourceSelector.SelectedIndex < 0)
            {
                MessageBox.Show("请选择输入源", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var layer = _layers[LayerSelector.SelectedIndex];
                var layerId = (int)layer.Tag;
                var inputSource = _inputSources[InputSourceSelector.SelectedIndex];

                var response = await _controller.SetLayerInputSource(layerId, inputSource.id);
                if (response.status == 0)
                {
                    UpdateStatus($"图层 {layerId} 输入源设置为 {inputSource.name}");
                }
                else
                {
                    UpdateStatus($"设置输入源失败: {response.message}");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"设置输入源异常: {ex.Message}");
            }
        }

        // 窗口大小改变时重新绘制网格
        protected override void OnRenderSizeChanged(SizeChangedInfo sizeInfo)
        {
            base.OnRenderSizeChanged(sizeInfo);
            if (IsLoaded)
            {
                DrawGrid();
            }
        }
    }
}
