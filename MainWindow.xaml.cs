using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Shapes;
using System.Windows.Threading;
using API.Models;

namespace API
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private DeviceController _controller = new DeviceController();
        private UIElement _draggingLayer;
        private Point _dragOffset;
        private List<Border> _layers = new List<Border>();
        private List<InputSource> _inputSources = new List<InputSource>();
        private int _nextLayerId = 1;

        // 图层选中和调整大小相关
        private Border _selectedLayer;
        private bool _isResizing = false;
        private string _resizeDirection = "";
        private Point _resizeStartPoint;
        private Rect _originalBounds;

        // zIndex管理
        private int _maxZIndex = 1;

        // 设备分辨率常量
        private const int DEVICE_WIDTH = 7680;
        private const int DEVICE_HEIGHT = 4320;

        public MainWindow()
        {
            InitializeComponent();

            // 延迟初始化，确保所有控件都已加载
            Loaded += MainWindow_Loaded;
        }

        private void MainWindow_Loaded(object sender, RoutedEventArgs e)
        {
            InitializeUI();
        }

        private void InitializeUI()
        {
            // 初始化状态
            UpdateConnectionStatus(false);
            UpdateLayerCount();
            UpdateLayerSelector(); // 初始化图层选择器

            // 添加坐标显示事件
            if (DisplayCanvas != null)
            {
                DisplayCanvas.MouseMove += DisplayCanvas_MouseMove;
            }

            // 初始化模拟模式
            UpdateSimulationModeStatus();

            // 绘制网格线（在窗口大小确定后）
            DrawGrid();
        }

        private void UpdateSimulationModeStatus()
        {
            if (SimulationModeCheckBox == null) return;

            bool isSimulationMode = true;//= SimulationModeCheckBox.IsChecked == true;
            _controller.SetSimulationMode(isSimulationMode);

            // 更新UI提示
            if (isSimulationMode)
            {
                Title = "设备控制模拟器 - LED拼接屏控制系统 [模拟模式]";
                UpdateStatus("模拟模式已启用 - 使用模拟数据进行测试");
            }
            else
            {
                Title = "设备控制模拟器 - LED拼接屏控制系统 [真实设备模式]";
                UpdateStatus("真实设备模式 - 将连接实际设备");
            }
        }

        private void SimulationMode_Changed(object sender, RoutedEventArgs e)
        {
            UpdateSimulationModeStatus();

            // 如果当前已连接，需要重新连接
            if (_controller.IsLoggedIn)
            {
                UpdateConnectionStatus(false);
                UpdateStatus("模式已切换，请重新登录");
            }
        }

        private void SimulateErrors_Changed(object sender, RoutedEventArgs e)
        {
            if (SimulateErrorsCheckBox == null) return;

            bool simulateErrors = true;//= SimulateErrorsCheckBox.IsChecked == true;
            _controller.SetSimulateErrors(simulateErrors);

            if (simulateErrors)
            {
                UpdateStatus("错误模拟已启用 - 部分操作可能会失败");
            }
            else
            {
                UpdateStatus("错误模拟已禁用 - 所有操作将成功");
            }
        }

        private void DrawGrid()
        {
            if (GridCanvas == null || DisplayCanvas == null) return;

            GridCanvas.Children.Clear();

            // 如果Canvas还没有实际大小，延迟绘制
            if (DisplayCanvas.ActualWidth <= 0 || DisplayCanvas.ActualHeight <= 0) return;

            // 绘制网格线（每500像素一条线）
            int gridSize = 500;
            var brush = new SolidColorBrush(Color.FromArgb(50, 255, 255, 255));

            // 垂直线
            for (int x = 0; x < DEVICE_WIDTH; x += gridSize)
            {
                var line = new Line
                {
                    X1 = x * (DisplayCanvas.ActualWidth / DEVICE_WIDTH),
                    Y1 = 0,
                    X2 = x * (DisplayCanvas.ActualWidth / DEVICE_WIDTH),
                    Y2 = DisplayCanvas.ActualHeight,
                    Stroke = brush,
                    StrokeThickness = 1
                };
                GridCanvas.Children.Add(line);
            }

            // 水平线
            for (int y = 0; y < DEVICE_HEIGHT; y += gridSize)
            {
                var line = new Line
                {
                    X1 = 0,
                    Y1 = y * (DisplayCanvas.ActualHeight / DEVICE_HEIGHT),
                    X2 = DisplayCanvas.ActualWidth,
                    Y2 = y * (DisplayCanvas.ActualHeight / DEVICE_HEIGHT),
                    Stroke = brush,
                    StrokeThickness = 1
                };
                GridCanvas.Children.Add(line);
            }
        }

        private void DisplayCanvas_MouseMove(object sender, MouseEventArgs e)
        {
            if (DisplayCanvas == null || CoordinateDisplay == null) return;
            if (DisplayCanvas.ActualWidth <= 0 || DisplayCanvas.ActualHeight <= 0) return;

            // 更新坐标显示
            var position = e.GetPosition(DisplayCanvas);
            var deviceX = (int)(position.X / DisplayCanvas.ActualWidth * DEVICE_WIDTH);
            var deviceY = (int)(position.Y / DisplayCanvas.ActualHeight * DEVICE_HEIGHT);
            CoordinateDisplay.Text = $"坐标: ({deviceX}, {deviceY})";
        }

        private void UpdateConnectionStatus(bool isConnected)
        {
            if (ConnectionStatus != null)
            {
                ConnectionStatus.Text = isConnected ? "已连接" : "未连接";
                ConnectionStatus.Foreground = isConnected ? Brushes.Green : Brushes.Red;
            }

            if (LoginButton != null)
            {
                LoginButton.Content = isConnected ? "已登录" : "登录设备";
                LoginButton.IsEnabled = !isConnected;
            }
        }

        private void UpdateLayerCount()
        {
            if (LayerCountText != null)
            {
                LayerCountText.Text = $"图层数: {_layers.Count}";
            }
        }

        private void UpdateLayerSelector(Border selectedLayer = null)
        {
            if (LayerSelector == null) return;

            // 重建图层选择器列表
            LayerSelector.Items.Clear();

            for (int i = 0; i < _layers.Count; i++)
            {
                var layer = _layers[i];
                var layerId = (int)layer.Tag;
                LayerSelector.Items.Add($"图层{layerId}");
            }

            // 设置选中项
            if (selectedLayer != null)
            {
                int index = _layers.IndexOf(selectedLayer);
                if (index >= 0)
                {
                    LayerSelector.SelectedIndex = index;
                }
            }
            else if (LayerSelector.Items.Count > 0)
            {
                LayerSelector.SelectedIndex = 0;
            }
        }

        private Border GetSelectedLayerFromSelector()
        {
            if (LayerSelector == null || LayerSelector.SelectedIndex < 0 || LayerSelector.SelectedIndex >= _layers.Count)
            {
                return null;
            }
            return _layers[LayerSelector.SelectedIndex];
        }

        private void UpdateStatus(string message)
        {
            if (StatusText != null)
            {
                StatusText.Text = message;
            }
        }

        private async void Login_Click(object sender, RoutedEventArgs e)
        {
            UpdateStatus("正在登录...");
            LoginButton.IsEnabled = false;

            try
            {
                var response = await _controller.LoginAsync();
                if (response.status == 0)
                {
                    UpdateConnectionStatus(true);
                    UpdateStatus("登录成功");

                    // 登录成功后刷新输入源列表
                    await RefreshInputSources();
                }
                else
                {
                    UpdateConnectionStatus(false);
                    UpdateStatus($"登录失败: {response.message}");
                    MessageBox.Show($"登录失败: {response.message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                UpdateConnectionStatus(false);
                UpdateStatus($"登录异常: {ex.Message}");
                MessageBox.Show($"登录异常: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                LoginButton.IsEnabled = !_controller.IsLoggedIn;
            }
        }

        private async Task RefreshInputSources()
        {
            try
            {
                var response = await _controller.GetInputSourcesAsync();
                if (response.status == 0)
                {
                    _inputSources = response.data.ToList();
                    InputSourceSelector.Items.Clear();

                    foreach (var source in _inputSources)
                    {
                        InputSourceSelector.Items.Add($"{source.name} ({source.type})");
                    }

                    if (_inputSources.Count > 0)
                    {
                        InputSourceSelector.SelectedIndex = 0;
                    }
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"刷新输入源失败: {ex.Message}");
            }
        }

        private async void CreateLayer_Click(object sender, RoutedEventArgs e)
        {
            //if (!_controller.IsLoggedIn)
            //{
            //    MessageBox.Show("请先登录设备", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            //    return;
            //}

            try
            {
                // 创建新图层，默认位置和大小
                int layerId = _nextLayerId++;
                int x = 100 + (_layers.Count * 50);
                int y = 100 + (_layers.Count * 50);
                int width = 800;
                int height = 600;

                var response = await _controller.CreateLayer(layerId, x, y, width, height);
                if (response.status == 0)
                {
                    CreateLayerUI(layerId, x, y, width, height);
                    UpdateStatus($"图层 {layerId} 创建成功");
                }
                else
                {
                    UpdateStatus($"创建图层失败: {response.message}");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"创建图层异常: {ex.Message}");
            }
        }

        private void CreateLayerUI(int layerId, int deviceX, int deviceY, int deviceWidth, int deviceHeight)
        {
            // 坐标换算：设备坐标转换为UI坐标
            double scaleX = DisplayCanvas.ActualWidth / DEVICE_WIDTH;
            double scaleY = DisplayCanvas.ActualHeight / DEVICE_HEIGHT;

            var layer = new Border
            {
                Name = $"Layer{layerId}",
                Background = GetRandomBrush(),
                BorderBrush = Brushes.White,
                BorderThickness = new Thickness(2),
                Width = deviceWidth * scaleX,
                Height = deviceHeight * scaleY,
                Cursor = Cursors.Hand
            };

            // 添加图层标签
            var textBlock = new TextBlock
            {
                Text = $"图层{layerId}",
                Foreground = Brushes.White,
                HorizontalAlignment = HorizontalAlignment.Center,
                VerticalAlignment = VerticalAlignment.Center,
                FontWeight = FontWeights.Bold
            };
            layer.Child = textBlock;

            Canvas.SetLeft(layer, deviceX * scaleX);
            Canvas.SetTop(layer, deviceY * scaleY);
            Canvas.SetZIndex(layer, _maxZIndex++); // 使用递增的zIndex

            layer.MouseLeftButtonDown += Layer_MouseDown;
            layer.MouseMove += Layer_MouseMove;
            layer.MouseLeftButtonUp += Layer_MouseUp;
            layer.MouseLeave += Layer_MouseLeave; // 添加鼠标离开事件
            layer.Tag = layerId; // 存储图层ID

            DisplayCanvas.Children.Add(layer);
            _layers.Add(layer);
            UpdateLayerCount();
            UpdateLayerSelector(layer); // 更新选择器并选中新创建的图层
        }

        private Brush GetRandomBrush()
        {
            var colors = new[] { Colors.Blue, Colors.Red, Colors.Green, Colors.Orange, Colors.Purple, Colors.Brown };
            var random = new Random();
            var color = colors[random.Next(colors.Length)];
            color.A = 180; // 半透明
            return new SolidColorBrush(color);
        }

        private void SelectLayer(Border layer)
        {
            // 取消之前选中的图层
            if (_selectedLayer != null)
            {
                _selectedLayer.BorderBrush = Brushes.White;
                _selectedLayer.BorderThickness = new Thickness(2);
            }

            // 选中新图层
            _selectedLayer = layer;
            if (_selectedLayer != null)
            {
                _selectedLayer.BorderBrush = Brushes.Yellow;
                _selectedLayer.BorderThickness = new Thickness(3);
                UpdateStatus($"已选中图层 {(int)_selectedLayer.Tag}");
            }
            else
            {
                UpdateStatus("已取消图层选中");
            }
        }

        private string GetResizeDirection(Border layer, Point position)
        {
            const double edgeThickness = 10; // 边缘检测厚度

            bool nearLeft = position.X <= edgeThickness;
            bool nearRight = position.X >= layer.Width - edgeThickness;
            bool nearTop = position.Y <= edgeThickness;
            bool nearBottom = position.Y >= layer.Height - edgeThickness;

            if (nearTop && nearLeft) return "NW";
            if (nearTop && nearRight) return "NE";
            if (nearBottom && nearLeft) return "SW";
            if (nearBottom && nearRight) return "SE";
            if (nearTop) return "N";
            if (nearBottom) return "S";
            if (nearLeft) return "W";
            if (nearRight) return "E";

            return ""; // 不在边缘
        }

        private void UpdateCursor(Border layer, string direction)
        {
            switch (direction)
            {
                case "N":
                case "S":
                    layer.Cursor = Cursors.SizeNS;
                    break;
                case "E":
                case "W":
                    layer.Cursor = Cursors.SizeWE;
                    break;
                case "NW":
                case "SE":
                    layer.Cursor = Cursors.SizeNWSE;
                    break;
                case "NE":
                case "SW":
                    layer.Cursor = Cursors.SizeNESW;
                    break;
                default:
                    layer.Cursor = Cursors.Hand;
                    break;
            }
        }

        private void Layer_MouseDown(object sender, MouseButtonEventArgs e)
        {
            var layer = sender as Border;
            if (layer == null) return;

            // 选中图层
            SelectLayer(layer);

            var position = e.GetPosition(layer);
            _resizeDirection = GetResizeDirection(layer, position);

            if (!string.IsNullOrEmpty(_resizeDirection))
            {
                // 开始调整大小
                _isResizing = true;
                _resizeStartPoint = e.GetPosition(DisplayCanvas);
                _originalBounds = new Rect(
                    Canvas.GetLeft(layer),
                    Canvas.GetTop(layer),
                    layer.Width,
                    layer.Height
                );
            }
            else
            {
                // 开始拖拽
                _draggingLayer = layer;
                _dragOffset = e.GetPosition(_draggingLayer);
            }

            layer.CaptureMouse();

            // 更新图层选择器
            UpdateLayerSelector(layer);
        }

        private void Layer_MouseMove(object sender, MouseEventArgs e)
        {
            var layer = sender as Border;
            if (layer == null) return;

            if (_isResizing && layer == _selectedLayer)
            {
                // 调整大小
                var currentPoint = e.GetPosition(DisplayCanvas);
                ResizeLayer(layer, currentPoint);
            }
            else if (_draggingLayer == layer)
            {
                // 拖拽移动
                var position = e.GetPosition(DisplayCanvas);
                Canvas.SetLeft(_draggingLayer, position.X - _dragOffset.X);
                Canvas.SetTop(_draggingLayer, position.Y - _dragOffset.Y);
            }
            else
            {
                // 更新鼠标光标
                var position = e.GetPosition(layer);
                var direction = GetResizeDirection(layer, position);
                UpdateCursor(layer, direction);
            }
        }

        private async void Layer_MouseUp(object sender, MouseButtonEventArgs e)
        {
            var layer = sender as Border;
            if (layer == null) return;

            // 立即释放鼠标捕获，防止继续移动
            layer.ReleaseMouseCapture();

            if (_isResizing || _draggingLayer == layer)
            {
                // 发送更新命令到后端
                await SendLayerUpdateToBackend(layer);
            }

            // 重置所有状态
            _isResizing = false;
            _draggingLayer = null;
            _resizeDirection = "";

            // 重置光标
            layer.Cursor = Cursors.Hand;
        }

        private void Layer_MouseLeave(object sender, MouseEventArgs e)
        {
            var layer = sender as Border;
            if (layer == null) return;

            // 如果不是在拖拽或调整大小状态，重置光标
            if (!_isResizing && _draggingLayer != layer)
            {
                layer.Cursor = Cursors.Hand;
            }
        }

        private void ResizeLayer(Border layer, Point currentPoint)
        {
            var deltaX = currentPoint.X - _resizeStartPoint.X;
            var deltaY = currentPoint.Y - _resizeStartPoint.Y;

            var newLeft = _originalBounds.Left;
            var newTop = _originalBounds.Top;
            var newWidth = _originalBounds.Width;
            var newHeight = _originalBounds.Height;

            switch (_resizeDirection)
            {
                case "N":
                    newTop += deltaY;
                    newHeight -= deltaY;
                    break;
                case "S":
                    newHeight += deltaY;
                    break;
                case "W":
                    newLeft += deltaX;
                    newWidth -= deltaX;
                    break;
                case "E":
                    newWidth += deltaX;
                    break;
                case "NW":
                    newLeft += deltaX;
                    newTop += deltaY;
                    newWidth -= deltaX;
                    newHeight -= deltaY;
                    break;
                case "NE":
                    newTop += deltaY;
                    newWidth += deltaX;
                    newHeight -= deltaY;
                    break;
                case "SW":
                    newLeft += deltaX;
                    newWidth -= deltaX;
                    newHeight += deltaY;
                    break;
                case "SE":
                    newWidth += deltaX;
                    newHeight += deltaY;
                    break;
            }

            // 限制最小尺寸
            const double minSize = 50;
            if (newWidth < minSize) newWidth = minSize;
            if (newHeight < minSize) newHeight = minSize;

            // 限制边界
            if (newLeft < 0) newLeft = 0;
            if (newTop < 0) newTop = 0;
            if (newLeft + newWidth > DisplayCanvas.ActualWidth)
                newWidth = DisplayCanvas.ActualWidth - newLeft;
            if (newTop + newHeight > DisplayCanvas.ActualHeight)
                newHeight = DisplayCanvas.ActualHeight - newTop;

            // 应用新的位置和大小
            Canvas.SetLeft(layer, newLeft);
            Canvas.SetTop(layer, newTop);
            layer.Width = newWidth;
            layer.Height = newHeight;
        }

        private async Task SendLayerUpdateToBackend(Border layer)
        {
            try
            {
                var layerId = (int)layer.Tag;

                // 坐标换算：UI坐标转换为设备坐标
                double scaleX = DisplayCanvas.ActualWidth / DEVICE_WIDTH;
                double scaleY = DisplayCanvas.ActualHeight / DEVICE_HEIGHT;

                int deviceX = (int)(Canvas.GetLeft(layer) / scaleX);
                int deviceY = (int)(Canvas.GetTop(layer) / scaleY);
                int deviceW = (int)(layer.Width / scaleX);
                int deviceH = (int)(layer.Height / scaleY);

                var response = await _controller.MoveResizeLayer(layerId, deviceX, deviceY, deviceW, deviceH);
                if (response.status == 0)
                {
                    UpdateStatus($"图层 {layerId} 更新成功 - 位置:({deviceX},{deviceY}) 大小:{deviceW}x{deviceH}");
                }
                else
                {
                    UpdateStatus($"更新图层失败: {response.message}");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"更新图层异常: {ex.Message}");
            }
        }

        private void Canvas_MouseMove(object sender, MouseEventArgs e)
        {
            // Canvas级别的鼠标移动处理（如果需要的话）
        }

        // Canvas级别的鼠标释放处理
        private void Canvas_MouseUp(object sender, MouseButtonEventArgs e)
        {
            // 强制重置所有拖拽状态，防止鼠标松开后继续移动
            if (_draggingLayer != null)
            {
                _draggingLayer.ReleaseMouseCapture();
                _draggingLayer = null;
            }

            _isResizing = false;
            _resizeDirection = "";

            // 如果点击的是Canvas空白区域，取消图层选中
            if (e.OriginalSource == DisplayCanvas)
            {
                SelectLayer(null);
            }
        }

        private async void CloseLayer_Click(object sender, RoutedEventArgs e)
        {
            //if (!_controller.IsLoggedIn)
            //{
            //    MessageBox.Show("请先登录设备", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            //    return;
            //}

            if (LayerSelector.SelectedIndex < 0 || LayerSelector.SelectedIndex >= _layers.Count)
            {
                MessageBox.Show("请选择要关闭的图层", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var layer = _layers[LayerSelector.SelectedIndex];
                var layerId = (int)layer.Tag;

                var response = await _controller.CloseLayer(layerId);
                if (response.status == 0)
                {
                    DisplayCanvas.Children.Remove(layer);
                    _layers.Remove(layer);

                    // 如果删除的是当前选中的图层，清除选中状态
                    if (_selectedLayer == layer)
                    {
                        _selectedLayer = null;
                    }

                    UpdateLayerCount();
                    UpdateLayerSelector(); // 重新构建选择器列表
                    UpdateStatus($"图层 {layerId} 关闭成功");
                }
                else
                {
                    UpdateStatus($"关闭图层失败: {response.message}");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"关闭图层异常: {ex.Message}");
            }
        }

        private async void BringToFront_Click(object sender, RoutedEventArgs e)
        {
            //if (!_controller.IsLoggedIn)
            //{
            //    MessageBox.Show("请先登录设备", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
            //    return;
            //}

            if (LayerSelector.SelectedIndex < 0 || LayerSelector.SelectedIndex >= _layers.Count)
            {
                MessageBox.Show("请选择要置顶的图层", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var layer = GetSelectedLayerFromSelector();
                if (layer == null)
                {
                    MessageBox.Show("请选择要置顶的图层", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var layerId = (int)layer.Tag;

                // 使用当前最大zIndex+1来确保置顶
                int newZIndex = _maxZIndex++;
                var response = await _controller.SetLayerZIndex(layerId, newZIndex);
                if (response.status == 0)
                {
                    Canvas.SetZIndex(layer, newZIndex);
                    UpdateStatus($"图层 {layerId} 置顶成功 (zIndex: {newZIndex})");
                }
                else
                {
                    UpdateStatus($"置顶图层失败: {response.message}");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"置顶图层异常: {ex.Message}");
            }
        }

        // 预设场景事件处理
        private async void ExperimentScene_Click(object sender, RoutedEventArgs e)
        {
            await ApplyPresetScene("实验投屏", 1);
        }

        private async void PresentationScene_Click(object sender, RoutedEventArgs e)
        {
            await ApplyPresetScene("演讲场景", 2);
        }

        private async void MonitorScene_Click(object sender, RoutedEventArgs e)
        {
            await ApplyPresetScene("监控场景", 3);
        }

        private async Task ApplyPresetScene(string sceneName, int presetId)
        {
            if (!_controller.IsLoggedIn)
            {
                MessageBox.Show("请先登录设备", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                UpdateStatus($"正在应用{sceneName}...");
                var response = await _controller.ApplyPresetScene(presetId);
                if (response.status == 0)
                {
                    UpdateStatus($"{sceneName}应用成功");
                    // 这里可以根据需要刷新UI显示
                }
                else
                {
                    UpdateStatus($"应用{sceneName}失败: {response.message}");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"应用{sceneName}异常: {ex.Message}");
            }
        }

        private async void RefreshInputSources_Click(object sender, RoutedEventArgs e)
        {
            if (!_controller.IsLoggedIn)
            {
                MessageBox.Show("请先登录设备", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            UpdateStatus("正在刷新输入源...");
            await RefreshInputSources();
            UpdateStatus("输入源刷新完成");
        }

        private async void ApplyInputToLayer_Click(object sender, RoutedEventArgs e)
        {
            if (!_controller.IsLoggedIn)
            {
                MessageBox.Show("请先登录设备", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var layer = GetSelectedLayerFromSelector();
            if (layer == null)
            {
                MessageBox.Show("请选择要设置的图层", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            if (InputSourceSelector.SelectedIndex < 0)
            {
                MessageBox.Show("请选择输入源", "提示", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var layerId = (int)layer.Tag;
                var inputSource = _inputSources[InputSourceSelector.SelectedIndex];

                var response = await _controller.SetLayerInputSource(layerId, inputSource.id);
                if (response.status == 0)
                {
                    UpdateStatus($"图层 {layerId} 输入源设置为 {inputSource.name}");
                }
                else
                {
                    UpdateStatus($"设置输入源失败: {response.message}");
                }
            }
            catch (Exception ex)
            {
                UpdateStatus($"设置输入源异常: {ex.Message}");
            }
        }

        // 窗口大小改变时重新绘制网格
        protected override void OnRenderSizeChanged(SizeChangedInfo sizeInfo)
        {
            base.OnRenderSizeChanged(sizeInfo);
            if (IsLoaded)
            {
                DrawGrid();
            }
        }
    }
}
