﻿using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using System.Windows.Threading;
using API.Models;

namespace API
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {
        private DeviceController _controller = new DeviceController();
        private UIElement _draggingLayer;
        private Point _dragOffset;

        public MainWindow()
        {
            InitializeComponent();
        }

        private async void Login_Click(object sender, RoutedEventArgs e)
        {
            if (await _controller.LoginAsync())
            {
                MessageBox.Show("登录成功");
            }
            else
            {
                MessageBox.Show("登录失败");
            }
        }

        private void Layer_MouseDown(object sender, MouseButtonEventArgs e)
        {
            _draggingLayer = sender as UIElement;
            _dragOffset = e.GetPosition(_draggingLayer);
            _draggingLayer.CaptureMouse();
        }

        private void Canvas_MouseMove(object sender, MouseEventArgs e)
        {
            if (_draggingLayer != null)
            {
                var canvas = (Canvas)sender;
                Point position = e.GetPosition(canvas);
                Canvas.SetLeft(_draggingLayer, position.X - _dragOffset.X);
                Canvas.SetTop(_draggingLayer, position.Y - _dragOffset.Y);
            }
        }

        private async void Canvas_MouseUp(object sender, MouseButtonEventArgs e)
        {
            if (_draggingLayer != null)
            {
                _draggingLayer.ReleaseMouseCapture();
                int layerId = int.Parse(_draggingLayer.Uid.Replace("Layer", ""));
                // 坐标换算（假设设备分辨率7680x4320）
                double scaleX = DisplayCanvas.ActualWidth / 7680.0;
                double scaleY = DisplayCanvas.ActualHeight / 4320.0;
                int deviceX = (int)(Canvas.GetLeft(_draggingLayer) / scaleX);
                int deviceY = (int)(Canvas.GetTop(_draggingLayer) / scaleY);
                int deviceW = (int)(_draggingLayer.RenderSize.Width / scaleX);
                int deviceH = (int)(_draggingLayer.RenderSize.Height / scaleY);
                try
                {
                    await _controller.MoveResizeLayer(layerId, deviceX, deviceY, deviceW, deviceH);
                }
                catch (Exception ex)
                {
                    Dispatcher.Invoke(() => MessageBox.Show($"错误: {ex.Message}"));
                }
                _draggingLayer = null;
            }
        }

        private async void CloseLayer_Click(object sender, RoutedEventArgs e)
        {
            int layerId = LayerSelector.SelectedIndex + 1;
            try
            {
                await _controller.CloseLayer(layerId);
            }
            catch (Exception ex)
            {
                Dispatcher.Invoke(() => MessageBox.Show($"错误: {ex.Message}"));
            }
        }

        private void BringToFront_Click(object sender, RoutedEventArgs e)
        {
            // 置顶操作可通过调整ZIndex实现
            int layerId = LayerSelector.SelectedIndex + 1;
            var layer = (Border)DisplayCanvas.FindName($"Layer{layerId}");
            if (layer != null)
            {
                Canvas.SetZIndex(layer, 99);
            }
        }
    }
}
