{"Version": 1, "WorkspaceRootPath": "E:\\work\\Csharp\\test\\1.API\\API\\", "Documents": [{"AbsoluteMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|e:\\work\\csharp\\test\\1.api\\api\\mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|solutionrelative:mainwindow.xaml.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|e:\\work\\csharp\\test\\1.api\\api\\models\\presetscene.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|solutionrelative:models\\presetscene.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|e:\\work\\csharp\\test\\1.api\\api\\models\\loginrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|solutionrelative:models\\loginrequest.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|e:\\work\\csharp\\test\\1.api\\api\\models\\layercommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|solutionrelative:models\\layercommand.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|e:\\work\\csharp\\test\\1.api\\api\\models\\inputsource.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|solutionrelative:models\\inputsource.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|e:\\work\\csharp\\test\\1.api\\api\\models\\apiresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|solutionrelative:models\\apiresponse.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}, {"AbsoluteMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|e:\\work\\csharp\\test\\1.api\\api\\deviceapiclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}", "RelativeMoniker": "D:0:0:{869951A4-929F-49F6-88E4-4254B2398490}|API.csproj|solutionrelative:deviceapiclient.cs||{A6C744A8-0E4A-4FC6-886A-064283054674}"}], "DocumentGroupContainers": [{"Orientation": 0, "VerticalTabListWidth": 256, "DocumentGroups": [{"DockedWidth": 200, "SelectedChildIndex": 1, "Children": [{"$type": "Bookmark", "Name": "ST:0:0:{aa2115a1-9712-457b-9047-dbb71ca2cdd2}"}, {"$type": "Document", "DocumentIndex": 0, "Title": "MainWindow.xaml.cs", "DocumentMoniker": "E:\\work\\Csharp\\test\\1.API\\API\\MainWindow.xaml.cs", "RelativeDocumentMoniker": "MainWindow.xaml.cs", "ToolTip": "E:\\work\\Csharp\\test\\1.API\\API\\MainWindow.xaml.cs*", "RelativeToolTip": "MainWindow.xaml.cs*", "ViewState": "AgIAAKcAAAAAAAAAAAAmwLcAAAAPAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T03:32:07.257Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 1, "Title": "PresetScene.cs", "DocumentMoniker": "E:\\work\\Csharp\\test\\1.API\\API\\Models\\PresetScene.cs", "RelativeDocumentMoniker": "Models\\PresetScene.cs", "ToolTip": "E:\\work\\Csharp\\test\\1.API\\API\\Models\\PresetScene.cs", "RelativeToolTip": "Models\\PresetScene.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T03:32:03.139Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 2, "Title": "LoginRequest.cs", "DocumentMoniker": "E:\\work\\Csharp\\test\\1.API\\API\\Models\\LoginRequest.cs", "RelativeDocumentMoniker": "Models\\LoginRequest.cs", "ToolTip": "E:\\work\\Csharp\\test\\1.API\\API\\Models\\LoginRequest.cs", "RelativeToolTip": "Models\\LoginRequest.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T03:32:01.737Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 3, "Title": "LayerCommand.cs", "DocumentMoniker": "E:\\work\\Csharp\\test\\1.API\\API\\Models\\LayerCommand.cs", "RelativeDocumentMoniker": "Models\\LayerCommand.cs", "ToolTip": "E:\\work\\Csharp\\test\\1.API\\API\\Models\\LayerCommand.cs", "RelativeToolTip": "Models\\LayerCommand.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T03:31:53.883Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 4, "Title": "InputSource.cs", "DocumentMoniker": "E:\\work\\Csharp\\test\\1.API\\API\\Models\\InputSource.cs", "RelativeDocumentMoniker": "Models\\InputSource.cs", "ToolTip": "E:\\work\\Csharp\\test\\1.API\\API\\Models\\InputSource.cs", "RelativeToolTip": "Models\\InputSource.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T03:31:19.506Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 5, "Title": "ApiResponse.cs", "DocumentMoniker": "E:\\work\\Csharp\\test\\1.API\\API\\Models\\ApiResponse.cs", "RelativeDocumentMoniker": "Models\\ApiResponse.cs", "ToolTip": "E:\\work\\Csharp\\test\\1.API\\API\\Models\\ApiResponse.cs", "RelativeToolTip": "Models\\ApiResponse.cs", "ViewState": "AgIAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T03:30:38.024Z", "EditorCaption": ""}, {"$type": "Document", "DocumentIndex": 6, "Title": "DeviceApiClient.cs", "DocumentMoniker": "E:\\work\\Csharp\\test\\1.API\\API\\DeviceApiClient.cs", "RelativeDocumentMoniker": "DeviceApiClient.cs", "ToolTip": "E:\\work\\Csharp\\test\\1.API\\API\\DeviceApiClient.cs", "RelativeToolTip": "DeviceApiClient.cs", "ViewState": "AgIAAAAAAAAAAAAAAADwvwcAAAABAAAAAAAAAA==", "Icon": "ae27a6b0-e345-4288-96df-5eaf394ee369.000738|", "WhenOpened": "2025-07-07T02:24:49.602Z", "EditorCaption": ""}]}]}]}