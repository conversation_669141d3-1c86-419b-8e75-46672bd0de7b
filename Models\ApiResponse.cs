namespace API.Models
{
    /// <summary>
    /// 通用API响应
    /// </summary>
    public class ApiResponse
    {
        public int status { get; set; }
        public string message { get; set; }
        public object data { get; set; }
    }

    /// <summary>
    /// 登录响应
    /// </summary>
    public class LoginResponse
    {
        public int status { get; set; }
        public string message { get; set; }
        public LoginData data { get; set; }
    }

    public class LoginData
    {
        public string token { get; set; }
        public UserInfo userInfo { get; set; }
    }

    public class UserInfo
    {
        public int id { get; set; }
        public string username { get; set; }
        public string role { get; set; }
    }

    /// <summary>
    /// 设备状态响应
    /// </summary>
    public class DeviceStatusResponse
    {
        public int status { get; set; }
        public string message { get; set; }
        public DeviceStatus data { get; set; }
    }

    public class DeviceStatus
    {
        public bool isOnline { get; set; }
        public string temperature { get; set; }
        public string resolution { get; set; }
        public int layerCount { get; set; }
        public string version { get; set; }
    }
}
