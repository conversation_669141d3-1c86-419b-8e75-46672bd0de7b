# 图层调整大小功能 - 快速测试指南

## 🚀 立即测试步骤（3分钟）

### 第一步：启动和准备（30秒）
1. 启动应用程序
2. 确认模拟模式已开启
3. 点击"登录设备"
4. 点击2-3次"新建窗口"创建多个图层

### 第二步：图层选中测试（30秒）
```
操作序列：
1. 点击任意图层 → 观察边框变为黄色
2. 点击另一个图层 → 观察选中切换
3. 点击Canvas空白区域 → 观察取消选中
4. 检查状态栏显示选中状态

✅ 通过标准：
- 选中图层显示黄色边框（3像素厚）
- 未选中图层显示白色边框（2像素厚）
- 状态栏正确显示选中状态
```

### 第三步：光标样式测试（1分钟）
```
操作序列：
1. 选中一个图层
2. 将鼠标移动到图层的上边缘 → 光标变为 ↕
3. 移动到右边缘 → 光标变为 ↔
4. 移动到右下角 → 光标变为 ↖↘
5. 移动到图层中心 → 光标变为 ✋

✅ 通过标准：
- 8个方向的光标样式正确
- 光标切换流畅无延迟
- 边缘检测区域合适（约10像素）
```

### 第四步：调整大小测试（1分钟）
```
操作序列：
1. 选中图层，鼠标移动到右下角
2. 按住左键向右下拖拽 → 图层变大
3. 松开鼠标 → 检查状态栏反馈
4. 移动到左边缘，向左拖拽 → 图层变窄
5. 移动到上边缘，向上拖拽 → 图层变矮

✅ 通过标准：
- 图层大小实时跟随鼠标变化
- 松开鼠标后状态栏显示更新成功
- 调整过程流畅无卡顿
```

## 🔍 详细功能验证

### 1. 八方向调整测试

| 测试项 | 操作 | 预期结果 | 验证点 |
|--------|------|----------|--------|
| 上边缘 | 向上拖拽 | 图层变矮，位置上移 | ✅ 高度减少，Y坐标减少 |
| 下边缘 | 向下拖拽 | 图层变高 | ✅ 高度增加，Y坐标不变 |
| 左边缘 | 向左拖拽 | 图层变窄，位置左移 | ✅ 宽度减少，X坐标减少 |
| 右边缘 | 向右拖拽 | 图层变宽 | ✅ 宽度增加，X坐标不变 |
| 左上角 | 向左上拖拽 | 同时调整宽高和位置 | ✅ 宽高减少，位置左上移 |
| 右上角 | 向右上拖拽 | 宽度增加，高度减少 | ✅ 宽度增加，高度减少，Y坐标减少 |
| 左下角 | 向左下拖拽 | 宽度减少，高度增加 | ✅ 宽度减少，高度增加，X坐标减少 |
| 右下角 | 向右下拖拽 | 宽高都增加 | ✅ 宽高都增加，位置不变 |

### 2. 边界限制测试

```
测试场景1：最小尺寸限制
1. 选中图层，从右下角向左上拖拽
2. 持续缩小直到无法再缩小
3. 验证最小尺寸为50x50像素

测试场景2：Canvas边界限制
1. 将图层拖拽到Canvas右边缘
2. 尝试向右调整大小
3. 验证图层不会超出Canvas边界

测试场景3：负坐标防护
1. 将图层拖拽到Canvas左上角
2. 从左上角向左上拖拽
3. 验证图层不会移动到负坐标
```

### 3. 后端同步测试

```
验证步骤：
1. 开启模拟模式
2. 调整图层大小
3. 观察状态栏显示的设备坐标
4. 验证坐标转换正确性

预期状态栏信息格式：
"图层 X 更新成功 - 位置:(deviceX,deviceY) 大小:deviceW x deviceH"

坐标转换验证：
- UI坐标 (100, 100) → 设备坐标 约 (1024, 576)
- UI坐标 (200, 200) → 设备坐标 约 (2048, 1152)
```

## 🐛 常见问题排查

### 问题1：光标样式不变化
**症状**：鼠标移动到边缘时光标不变
**排查步骤**：
1. 确认图层已选中（黄色边框）
2. 检查鼠标是否在10像素边缘区域内
3. 尝试重新选中图层

### 问题2：调整大小无响应
**症状**：拖拽时图层大小不变
**排查步骤**：
1. 确认从边缘开始拖拽
2. 检查是否正确按住鼠标左键
3. 验证图层是否已选中

### 问题3：调整后无状态反馈
**症状**：松开鼠标后状态栏无更新
**排查步骤**：
1. 检查网络连接（模拟模式下应该正常）
2. 查看控制台是否有异常信息
3. 确认模拟模式已启用

### 问题4：图层选中状态异常
**症状**：多个图层同时显示选中状态
**排查步骤**：
1. 点击Canvas空白区域取消所有选中
2. 重新选中单个图层
3. 如果问题持续，重启应用

## 📊 性能测试

### 响应时间测试
```
测试项目：
1. 图层选中响应时间 < 100ms
2. 光标样式切换时间 < 50ms
3. 调整大小响应时间 < 50ms
4. 后端命令发送时间 < 500ms（模拟模式）

测试方法：
- 连续快速操作
- 观察是否有明显延迟
- 检查操作是否丢失
```

### 稳定性测试
```
压力测试：
1. 创建10个图层
2. 快速切换选中状态
3. 连续调整多个图层大小
4. 运行10分钟无异常

内存测试：
1. 长时间操作
2. 观察内存使用情况
3. 检查是否有内存泄漏
```

## ✅ 完整测试清单

### 基础功能
- [ ] 图层选中/取消选中正常
- [ ] 8种光标样式正确显示
- [ ] 8个方向调整大小正常
- [ ] 状态栏反馈及时准确

### 边界条件
- [ ] 最小尺寸限制生效
- [ ] Canvas边界限制生效
- [ ] 负坐标防护正常
- [ ] 超大尺寸处理正常

### 用户体验
- [ ] 操作直观易懂
- [ ] 视觉反馈清晰
- [ ] 响应速度快
- [ ] 无明显卡顿

### 后端集成
- [ ] 坐标转换正确
- [ ] API调用成功
- [ ] 错误处理完善
- [ ] 模拟模式正常

## 🎯 测试通过标准

### 必须满足（关键功能）
- ✅ 图层选中机制正常工作
- ✅ 调整大小功能完全可用
- ✅ 后端命令正确发送
- ✅ 无功能性异常

### 期望达到（用户体验）
- ✅ 操作流畅自然
- ✅ 视觉反馈及时
- ✅ 边界处理合理
- ✅ 性能表现良好

## 🚀 测试完成后

### 如果测试通过
1. 可以进行更复杂的场景测试
2. 准备真实设备环境测试
3. 考虑添加更多增强功能

### 如果发现问题
1. 记录详细的问题描述
2. 提供重现步骤
3. 截图或录屏问题现象
4. 报告给开发团队修复

现在请按照这个指南测试新的图层调整大小功能！
