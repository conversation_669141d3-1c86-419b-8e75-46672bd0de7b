# LED拼接屏控制系统模拟器 - 使用说明

## 界面布局说明

### 主界面分为四个区域：

#### 1. 设备控制区域（顶部）
- **登录设备按钮**：连接到LED拼接屏设备
- **连接状态显示**：显示当前连接状态（红色=未连接，绿色=已连接）
- **图层选择下拉框**：选择要操作的图层
- **新建窗口按钮**：创建新的显示图层
- **关闭窗口按钮**：删除选中的图层
- **置顶按钮**：将选中图层置于最前端

#### 2. 场景管理区域（第二行）
- **实验投屏按钮**：应用实验室投屏预设场景
- **演讲场景按钮**：应用会议演讲预设场景
- **监控场景按钮**：应用监控显示预设场景
- **信号源下拉框**：选择输入信号源
- **刷新信号源按钮**：更新可用信号源列表
- **应用到图层按钮**：将选中信号源应用到当前图层

#### 3. 模拟显示屏区域（主要区域）
- **黑色背景**：模拟LED拼接屏显示区域
- **网格线**：辅助定位的参考线（每500像素一条）
- **彩色图层块**：代表实际的显示窗口
- **坐标显示**：右上角显示当前鼠标位置的设备坐标

#### 4. 状态栏（底部）
- **状态信息**：显示当前操作状态和结果
- **图层计数**：显示当前图层数量
- **分辨率信息**：显示设备分辨率（7680x4320）

## 操作流程

### 第一步：设备连接
1. 启动应用程序
2. 点击"登录设备"按钮
3. 等待连接状态变为"已连接"（绿色）
4. 状态栏显示"登录成功"

### 第二步：创建图层
1. 点击"新建窗口"按钮
2. 在显示区域会出现一个彩色的图层块
3. 图层会自动分配一个ID和颜色
4. 状态栏显示创建成功信息

### 第三步：操作图层
#### 移动图层：
1. 用鼠标左键点击图层块
2. 拖拽到目标位置
3. 松开鼠标，系统自动同步到设备

#### 关闭图层：
1. 在图层选择框中选择要关闭的图层
2. 点击"关闭窗口"按钮
3. 图层从显示区域消失

#### 置顶图层：
1. 选择要置顶的图层
2. 点击"置顶"按钮
3. 图层显示在最前端

### 第四步：场景管理
#### 应用预设场景：
1. 点击任一场景按钮（实验投屏/演讲场景/监控场景）
2. 系统自动应用预定义的图层布局
3. 状态栏显示应用结果

#### 设置信号源：
1. 点击"刷新信号源"获取最新信号源列表
2. 在信号源下拉框中选择目标信号源
3. 在图层选择框中选择目标图层
4. 点击"应用到图层"按钮

## 坐标系统说明

### 设备坐标系
- **原点**：左上角 (0, 0)
- **X轴**：向右递增，最大值 7679
- **Y轴**：向下递增，最大值 4319
- **单位**：像素

### 坐标显示
- 鼠标移动时，右上角实时显示对应的设备坐标
- 格式：坐标: (X, Y)
- 例如：坐标: (3840, 2160) 表示屏幕中心位置

## 图层颜色说明
系统自动为每个图层分配不同颜色：
- 蓝色、红色、绿色、橙色、紫色、棕色
- 颜色半透明，便于观察重叠效果
- 图层中心显示图层编号

## 网格线说明
- 白色半透明网格线
- 间距：500像素
- 用于辅助定位和对齐
- 不影响实际显示效果

## 状态信息说明

### 连接状态
- **未连接**（红色）：设备未连接或登录失败
- **已连接**（绿色）：设备连接正常，可以进行操作

### 操作状态
- **就绪**：系统准备接受操作
- **正在登录...**：正在尝试连接设备
- **登录成功**：设备连接成功
- **图层X创建成功**：新图层创建完成
- **图层X移动成功**：图层位置更新完成
- **错误信息**：显示具体的错误原因

## 注意事项

### 操作限制
1. 必须先登录设备才能进行图层操作
2. 图层选择框只显示已创建的图层
3. 信号源需要设备在线才能获取

### 性能建议
1. 避免频繁拖拽图层，每次移动都会发送API请求
2. 大量图层操作时建议使用预设场景
3. 定期刷新信号源列表以获取最新状态

### 故障排除
1. **连接失败**：检查设备IP地址和网络连接
2. **图层操作无响应**：确认设备连接状态
3. **坐标不准确**：检查设备分辨率设置

## 快捷操作技巧

### 鼠标操作
- **左键点击图层**：选中并开始拖拽
- **拖拽图层**：移动到新位置
- **鼠标悬停**：查看实时坐标

### 键盘操作
- **Tab键**：在控件间切换焦点
- **Enter键**：执行默认按钮操作
- **Esc键**：取消当前拖拽操作

### 批量操作
1. 使用预设场景快速布局多个图层
2. 先创建多个图层，再逐一设置信号源
3. 利用图层选择框快速切换操作目标

## 扩展功能建议

### 即将支持的功能
- 图层大小调整（拖拽边角）
- 图层属性编辑（透明度、旋转）
- 自定义预设场景保存
- 操作历史记录和撤销
- 批量图层操作
- 快捷键支持

### 高级功能规划
- 多屏幕支持
- 实时预览
- 远程控制
- 定时任务
- 日志记录
- 配置文件管理
